import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/common/common_elevated_button.dart';
import '../../../widgets/dialogs/document_upload_dialogs.dart';

class StepWorkDetailsEnhanced extends StatefulWidget {
  final Function(Map<String, dynamic>)? onContinue;
  final VoidCallback? onBack;

  const StepWorkDetailsEnhanced({
    super.key,
    this.onContinue,
    this.onBack,
  });

  @override
  State<StepWorkDetailsEnhanced> createState() => _StepWorkDetailsEnhancedState();
}

class _StepWorkDetailsEnhancedState extends State<StepWorkDetailsEnhanced> {
  // Work details data
  String? _selectedShift;
  String? _selectedVehicle;
  String? _deliveryPreference;
  String? _weekOff;
  String? _referralCode;

  final _vehicleNumberController = TextEditingController();

  @override
  void dispose() {
    _vehicleNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),

              // Title
              const Text(
                'Work Settings',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),

              // Subtitle
              const Text(
                'Configure your work preferences',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 32),

              // Form
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Shift Selection
                      _buildSimpleDropdown(
                        label: 'Preferred Shift',
                        value: _selectedShift,
                        items: ['Morning', 'Evening', 'Night'],
                        onChanged: (value) => setState(() => _selectedShift = value),
                      ),
                      const SizedBox(height: 16),

                      // Vehicle Type
                      _buildSimpleDropdown(
                        label: 'Vehicle Type',
                        value: _selectedVehicle,
                        items: ['Bike', 'Bicycle', 'Scooter'],
                        onChanged: (value) => setState(() => _selectedVehicle = value),
                      ),
                      const SizedBox(height: 16),

                      // Vehicle Number
                      TextFormField(
                        controller: _vehicleNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Vehicle Number',
                          border: OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Delivery Preference
                      _buildSimpleDropdown(
                        label: 'Delivery Preference',
                        value: _deliveryPreference,
                        items: ['Food', 'Grocery', 'Both'],
                        onChanged: (value) => setState(() => _deliveryPreference = value),
                      ),
                      const SizedBox(height: 16),

                      // Week Off
                      _buildSimpleDropdown(
                        label: 'Preferred Week Off',
                        value: _weekOff,
                        items: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
                        onChanged: (value) => setState(() => _weekOff = value),
                      ),
                      const SizedBox(height: 16),

                      // Referral Code
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Referral Code (Optional)',
                          border: OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        onChanged: (value) => _referralCode = value,
                      ),
                    ],
                  ),
                ),
              ),

              // Continue Button
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () {
                    final data = {
                      'shift': _selectedShift,
                      'vehicle': _selectedVehicle,
                      'vehicleNumber': _vehicleNumberController.text,
                      'deliveryPreference': _deliveryPreference,
                      'weekOff': _weekOff,
                      'referralCode': _referralCode,
                    };
                    widget.onContinue?.call(data);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.white,
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: Text(item),
      )).toList(),
      onChanged: onChanged,
    );
  }
}
