import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/mixins/analytics_mixin.dart';

class RegistrationWelcomeScreen extends StatefulWidget {
  const RegistrationWelcomeScreen({super.key});

  @override
  State<RegistrationWelcomeScreen> createState() => _RegistrationWelcomeScreenState();
}

class _RegistrationWelcomeScreenState extends State<RegistrationWelcomeScreen> with AnalyticsMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<Map<String, dynamic>> _onboardingData = [
    {
      'title': 'Welcome to KisanKonnect',
      'subtitle': 'Join thousands of delivery partners',
      'description': 'Start your journey as a delivery partner and earn money on your own schedule.',
      'icon': Icons.delivery_dining,
      'color': AppColors.green,
    },
    {
      'title': 'Flexible Working Hours',
      'subtitle': 'Work when you want',
      'description': 'Choose your own working hours and shifts that fit your lifestyle.',
      'icon': Icons.schedule_outlined,
      'color': Colors.blue,
    },
    {
      'title': 'Earn More Money',
      'subtitle': 'Competitive earnings',
      'description': 'Earn competitive rates with bonuses and incentives for top performers.',
      'icon': Icons.monetization_on_outlined,
      'color': Colors.orange,
    },
    {
      'title': 'Quick Registration',
      'subtitle': 'Get started in minutes',
      'description': 'Simple registration process to get you started quickly and easily.',
      'icon': Icons.rocket_launch_outlined,
      'color': Colors.purple,
    },
  ];

  @override
  void initState() {
    super.initState();
    
    // Track screen view
    trackScreenView('registration_welcome_screen');
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header with skip button
            _buildHeader(),
            
            // Onboarding content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() => _currentPage = index);
                },
                itemCount: _onboardingData.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(_onboardingData[index]);
                },
              ),
            ),
            
            // Bottom section with indicators and buttons
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo or app name
          Row(
            children: [
              Container(
                width: ResponsiveUtils.scale(context, 32),
                height: ResponsiveUtils.scale(context, 32),
                decoration: BoxDecoration(
                  color: AppColors.green,
                  borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                ),
                child: Icon(
                  Icons.agriculture,
                  color: Colors.white,
                  size: ResponsiveUtils.scale(context, 20),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingS(context)),
              Text(
                'KisanKonnect',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 18),
                  fontWeight: FontWeight.bold,
                  color: AppColors.green,
                ),
              ),
            ],
          ),
          
          // Skip button
          if (_currentPage < _onboardingData.length - 1)
            TextButton(
              onPressed: _skipToRegistration,
              child: Text(
                'Skip',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 14),
                  color: Colors.grey.shade600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOnboardingPage(Map<String, dynamic> data) {
    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: ResponsiveUtils.scale(context, 120),
            height: ResponsiveUtils.scale(context, 120),
            decoration: BoxDecoration(
              color: data['color'].withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              data['icon'],
              size: ResponsiveUtils.scale(context, 60),
              color: data['color'],
            ),
          ),
          
          SizedBox(height: ResponsiveUtils.spacingXL(context)),
          
          // Title
          Text(
            data['title'],
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 24),
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: ResponsiveUtils.spacingS(context)),
          
          // Subtitle
          Text(
            data['subtitle'],
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w500,
              color: data['color'],
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          
          // Description
          Text(
            data['description'],
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 14),
              color: Colors.grey.shade600,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      child: Column(
        children: [
          // Page indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _onboardingData.length,
              (index) => _buildPageIndicator(index),
            ),
          ),
          
          SizedBox(height: ResponsiveUtils.spacingL(context)),
          
          // Action buttons
          Row(
            children: [
              // Previous button
              if (_currentPage > 0)
                Expanded(
                  child: OutlinedButton(
                    onPressed: _previousPage,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.green),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                      ),
                      padding: EdgeInsets.symmetric(vertical: ResponsiveUtils.spacingM(context)),
                    ),
                    child: Text(
                      'Previous',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 16),
                        fontWeight: FontWeight.w600,
                        color: AppColors.green,
                      ),
                    ),
                  ),
                ),
              
              if (_currentPage > 0) SizedBox(width: ResponsiveUtils.spacingM(context)),
              
              // Next/Get Started button
              Expanded(
                flex: _currentPage == 0 ? 1 : 1,
                child: ElevatedButton(
                  onPressed: _currentPage == _onboardingData.length - 1 
                      ? _startRegistration 
                      : _nextPage,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                    ),
                    padding: EdgeInsets.symmetric(vertical: ResponsiveUtils.spacingM(context)),
                  ),
                  child: Text(
                    _currentPage == _onboardingData.length - 1 ? 'Get Started' : 'Next',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 16),
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final isActive = index == _currentPage;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingXS(context)),
      width: isActive ? ResponsiveUtils.scale(context, 24) : ResponsiveUtils.scale(context, 8),
      height: ResponsiveUtils.scale(context, 8),
      decoration: BoxDecoration(
        color: isActive ? AppColors.green : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      
      // Track page navigation
      trackUserAction('onboarding_next_page', properties: {
        'current_page': _currentPage,
        'next_page': _currentPage + 1,
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      
      // Track page navigation
      trackUserAction('onboarding_previous_page', properties: {
        'current_page': _currentPage,
        'previous_page': _currentPage - 1,
      });
    }
  }

  void _skipToRegistration() {
    // Track skip action
    trackUserAction('onboarding_skipped', properties: {
      'skipped_at_page': _currentPage,
      'total_pages': _onboardingData.length,
    });
    
    _startRegistration();
  }

  void _startRegistration() {
    // Track registration start
    trackUserAction('registration_started', properties: {
      'source': 'onboarding_welcome',
      'completed_pages': _currentPage + 1,
      'total_pages': _onboardingData.length,
    });
    
    // Navigate to phone number entry
    Get.offNamed(AppRoutes.enterNumber);
  }
}
