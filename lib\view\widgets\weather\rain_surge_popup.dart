import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Rain Surge Popup Dialog - shows detailed rain incentive information
class RainSurgePopup extends StatelessWidget {
  final String title;
  final String amount;
  final String description;
  final String detailMessage;

  const RainSurgePopup({
    super.key,
    this.title = "Rainy day special!",
    this.amount = "₹25",
    this.description = "extra per order with Rain mode activated",
    this.detailMessage = "Earn extra incentives for every order delivered during rain. Stay safe, earn more!",
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close button
            Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 20,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Rain icon with background
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFFE8F5E8), // Light green background
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.umbrella,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Rain emoji and title
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "🌧️",
                  style: TextStyle(fontSize: 20),
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTextTheme.cardTitle.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Amount and description
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: AppTextTheme.cardSubtitle.copyWith(
                  fontSize: 16,
                  color: Colors.black87,
                ),
                children: [
                  const TextSpan(text: "Earn "),
                  TextSpan(
                    text: amount,
                    style: const TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  TextSpan(text: " $description"),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Detail message
            Text(
              detailMessage,
              textAlign: TextAlign.center,
              style: AppTextTheme.cardCaption.copyWith(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Okay button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  "Okay",
                  style: AppTextTheme.cardTitle.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show the rain surge popup
  static void show(BuildContext context, {
    String? title,
    String? amount,
    String? description,
    String? detailMessage,
  }) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => RainSurgePopup(
        title: title ?? "Rainy day special!",
        amount: amount ?? "₹25",
        description: description ?? "extra per order with Rain mode activated",
        detailMessage: detailMessage ?? "Earn extra incentives for every order delivered during rain. Stay safe, earn more!",
      ),
    );
  }
}
