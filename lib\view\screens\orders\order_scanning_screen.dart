import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class OrderScanningScreen extends StatefulWidget {
  const OrderScanningScreen({super.key});

  @override
  State<OrderScanningScreen> createState() => _OrderScanningScreenState();
}

class _OrderScanningScreenState extends State<OrderScanningScreen> {
  late PickupSession _pickupSession;
  PickupOrderItem? _currentScanningOrder;
  bool _isScanning = false;
  bool _showCompletionMessage = false;
  Timer? _autoAdvanceTimer;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  @override
  void dispose() {
    _autoAdvanceTimer?.cancel();
    super.dispose();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _pickupSession = args?['pickupSession'] ?? _createDefaultSession();
    _currentScanningOrder = _pickupSession.nextOrderToScan;
  }

  PickupSession _createDefaultSession() {
    // Fallback if no session provided
    return PickupSession(
      sessionId: 'default_session',
      orders: const [],
      startTime: DateTime.now(),
    );
  }

  void _startScanning() {
    if (_currentScanningOrder == null) return;

    setState(() {
      _isScanning = true;
    });

    // Simulate QR scanning after 2 seconds
    Timer(const Duration(seconds: 2), () {
      _simulateScanSuccess();
    });
  }

  void _simulateScanSuccess() {
    if (_currentScanningOrder == null) return;

    final order = _currentScanningOrder!;
    final phase = order.currentScanningPhase;

    // Update scanned items based on current phase
    List<String> newScannedSaddleBags = List.from(order.scannedSaddleBags);
    List<String> newScannedSilverBags = List.from(order.scannedSilverBags);
    List<String> newScannedMilkPouches = List.from(order.scannedMilkPouches);

    switch (phase) {
      case ScanningPhase.saddleBags:
        newScannedSaddleBags.add('saddle_${newScannedSaddleBags.length + 1}');
        break;
      case ScanningPhase.silverBags:
        newScannedSilverBags.add('silver_${newScannedSilverBags.length + 1}');
        break;
      case ScanningPhase.milkPouches:
        newScannedMilkPouches.add('milk_${newScannedMilkPouches.length + 1}');
        break;
      case ScanningPhase.completed:
        break;
    }

    // Update the order in the session
    final updatedOrder = order.copyWith(
      scannedSaddleBags: newScannedSaddleBags,
      scannedSilverBags: newScannedSilverBags,
      scannedMilkPouches: newScannedMilkPouches,
      isScanned: newScannedSaddleBags.length == order.saddleBags &&
          newScannedSilverBags.length == order.silverBags &&
          newScannedMilkPouches.length == order.milkPouches,
    );

    final updatedOrders = _pickupSession.orders.map((o) {
      return o.orderId == order.orderId ? updatedOrder : o;
    }).toList();

    setState(() {
      _pickupSession = _pickupSession.copyWith(orders: updatedOrders);
      _currentScanningOrder = updatedOrder;
      _isScanning = false;
    });

    // Check if current order is complete
    if (updatedOrder.isCompletelyScanned) {
      _moveToNextOrder();
    }

    // Check if all orders are complete
    if (_pickupSession.allOrdersScanned) {
      _showCompletionScreen();
    }
  }

  void _moveToNextOrder() {
    final nextOrder = _pickupSession.nextOrderToScan;
    setState(() {
      _currentScanningOrder = nextOrder;
    });
  }

  void _showCompletionScreen() {
    setState(() {
      _showCompletionMessage = true;
    });

    // Auto-advance after 5 seconds
    _autoAdvanceTimer = Timer(const Duration(seconds: 5), () {
      _navigateToNextScreen();
    });
  }

  void _navigateToNextScreen() {
    Get.offNamed(AppRoutes.orderPickedUp, arguments: {
      'pickupSession': _pickupSession,
    });
  }

  void _selectSpecificOrder(PickupOrderItem order) {
    setState(() {
      _currentScanningOrder = order;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showCompletionMessage) {
      return _buildCompletionScreen();
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Scan Orders',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Progress header
          _buildProgressHeader(),

          // QR Scanner section
          Expanded(
            flex: 3,
            child: _buildQRScannerSection(),
          ),

          // Orders list section
          Expanded(
            flex: 2,
            child: _buildOrdersList(),
          ),

          // Bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildProgressHeader() {
    final scannedCount = _pickupSession.scannedOrdersCount;
    final totalCount = _pickupSession.displayableOrders.length;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Text(
            _currentScanningOrder?.scanningProgressText ?? 'Select an order to scan',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                '$scannedCount/$totalCount Orders',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: LinearProgressIndicator(
                  value: totalCount == 0 ? 0 : scannedCount / totalCount,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                  minHeight: 6,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQRScannerSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Scanner header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  color: AppColors.green,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _currentScanningOrder != null
                        ? 'Scanning Order #${_currentScanningOrder!.orderId}'
                        : 'QR Code Scanner',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Scanner area
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // QR scanner frame
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.green,
                        width: 3,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Stack(
                      children: [
                        // Corner decorations
                        ...List.generate(4, (index) => _buildCornerDecoration(index)),

                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (_isScanning)
                                const CircularProgressIndicator()
                              else
                                Icon(
                                  Icons.qr_code_2,
                                  size: 60,
                                  color: AppColors.green.withValues(alpha: 0.7),
                                ),
                              const SizedBox(height: 12),
                              Text(
                                _isScanning ? 'Scanning...' : 'Align QR code within frame',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Current scanning instruction
                  if (_currentScanningOrder != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _currentScanningOrder!.scanningProgressText,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Scan button
                  ElevatedButton(
                    onPressed: _isScanning || _currentScanningOrder == null ? null : _startScanning,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.green,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: _isScanning
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Tap to Scan',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    final displayableOrders = _pickupSession.displayableOrders;

    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // List header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Orders to Scan (${displayableOrders.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),

          // Orders list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: displayableOrders.length,
              itemBuilder: (context, index) {
                final order = displayableOrders[index];
                return _buildOrderItem(order, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(PickupOrderItem order, int index) {
    final isSelected = _currentScanningOrder?.orderId == order.orderId;
    final isCompleted = order.isCompletelyScanned;

    return GestureDetector(
      onTap: () => _selectSpecificOrder(order),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isCompleted
              ? AppColors.green.withValues(alpha: 0.1)
              : (isSelected ? Colors.blue.withValues(alpha: 0.1) : Colors.grey.shade50),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isCompleted ? AppColors.green : (isSelected ? Colors.blue : Colors.grey.shade300),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Red rack number (clickable)
            GestureDetector(
              onTap: () => _selectSpecificOrder(order),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    order.columnNumber.toString(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Order details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order #${order.orderId}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    order.scanningProgressText,
                    style: TextStyle(
                      fontSize: 12,
                      color: isCompleted ? AppColors.green : Colors.black54,
                      fontWeight: isCompleted ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),

            // Status icon
            Icon(
              isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isCompleted ? AppColors.green : Colors.grey.shade400,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButton() {
    final allScanned = _pickupSession.allOrdersScanned;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: allScanned ? _navigateToNextScreen : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: allScanned ? AppColors.green : Colors.grey,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
          ),
          child: Text(
            allScanned ? 'Next' : 'Scan All Orders',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionScreen() {
    return Scaffold(
      backgroundColor: AppColors.green,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Success icon
            Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                size: 60,
                color: Colors.green,
              ),
            ),

            const SizedBox(height: 32),

            // Success message
            const Text(
              'All Orders Scanned!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Scanned ${_pickupSession.scannedOrdersCount} orders successfully',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 32),

            // Auto-advance message
            const Text(
              'Moving to next step automatically...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),

            const SizedBox(height: 16),

            // Manual next button
            ElevatedButton(
              onPressed: _navigateToNextScreen,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppColors.green,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text(
                'Continue Now',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCornerDecoration(int index) {
    final positions = [
      const Alignment(-1, -1), // Top-left
      const Alignment(1, -1), // Top-right
      const Alignment(-1, 1), // Bottom-left
      const Alignment(1, 1), // Bottom-right
    ];

    return Positioned.fill(
      child: Align(
        alignment: positions[index],
        child: Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.green,
              width: 3,
            ),
          ),
        ),
      ),
    );
  }
}
