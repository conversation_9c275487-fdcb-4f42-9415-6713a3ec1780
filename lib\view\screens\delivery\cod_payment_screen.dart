import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class CODPaymentScreen extends StatefulWidget {
  const CODPaymentScreen({super.key});

  @override
  State<CODPaymentScreen> createState() => _CODPaymentScreenState();
}

class _CODPaymentScreenState extends State<CODPaymentScreen> {
  late DeliveryOrderItem _currentOrder;
  late double _totalAmount;

  String _selectedPaymentMode = ''; // 'cash', 'qr', 'cash_qr'
  final TextEditingController _cashAmountController = TextEditingController();

  bool _isProcessing = false;
  bool _showQRCode = false;
  bool _paymentReceived = false;

  double _cashAmount = 0.0;
  double _qrAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  @override
  void dispose() {
    _cashAmountController.dispose();
    super.dispose();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _currentOrder = args?['currentOrder'] ??
        const DeliveryOrderItem(
          orderId: 'E76592',
          customerName: 'John Doe',
          address: 'Sector 15, Vashi, Navi Mumbai',
          distance: 2.5,
          priority: 1,
          estimatedTime: '15 min',
          paymentMode: 'COD',
          bagCount: 3,
        );

    _totalAmount = args?['totalAmount'] ?? 450.0;
  }

  void _selectPaymentMode(String mode) {
    setState(() {
      _selectedPaymentMode = mode;
      _showQRCode = false;
      _paymentReceived = false;
    });

    switch (mode) {
      case 'cash':
        _showCashPaymentDialog();
        break;
      case 'qr':
        _showQRPayment();
        break;
      case 'cash_qr':
        _showCashQRPaymentDialog();
        break;
    }
  }

  void _showCashPaymentDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Cash Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Amount to collect: ₹${_totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Please collect the exact amount from customer',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _markPaymentReceived();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Received', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showQRPayment() {
    setState(() {
      _showQRCode = true;
      _qrAmount = _totalAmount;
    });
  }

  void _showCashQRPaymentDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Cash + QR Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Amount: ₹${_totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _cashAmountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Cash Amount Received',
                border: OutlineInputBorder(),
                prefixText: '₹',
              ),
              onChanged: (value) {
                final cash = double.tryParse(value) ?? 0.0;
                setState(() {
                  _cashAmount = cash;
                  _qrAmount = _totalAmount - cash;
                });
              },
            ),
            const SizedBox(height: 12),
            Text(
              'QR Amount: ₹${_qrAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _cashAmount > 0 && _qrAmount >= 0
                ? () {
                    Get.back();
                    _showQRForBalance();
                  }
                : null,
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Next', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showQRForBalance() {
    setState(() {
      _showQRCode = true;
    });
  }

  void _markPaymentReceived() {
    setState(() {
      _paymentReceived = true;
      _isProcessing = true;
    });

    // Simulate processing delay
    Future.delayed(const Duration(seconds: 2), () {
      _showPaymentReceivedDialog();
    });
  }

  void _showPaymentReceivedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.green, size: 24),
            const SizedBox(width: 8),
            const Text('Payment Received'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Amount: ₹${_totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Payment Mode: ${_getPaymentModeText()}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Get.back();
              _proceedToDeliveryCompletion();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Continue', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  String _getPaymentModeText() {
    switch (_selectedPaymentMode) {
      case 'cash':
        return 'Cash';
      case 'qr':
        return 'QR Code';
      case 'cash_qr':
        return 'Cash + QR Code';
      default:
        return 'Unknown';
    }
  }

  void _proceedToDeliveryCompletion() {
    Get.toNamed(AppRoutes.deliveryCompletion, arguments: {
      'currentOrder': _currentOrder,
      'paymentMode': _selectedPaymentMode,
      'totalAmount': _totalAmount,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Payment Collection',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Order details header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order #${_currentOrder.orderId}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentOrder.customerName,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.green.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total Amount:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        '₹${_totalAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Payment options
          Expanded(
            child: _showQRCode ? _buildQRCodeSection() : _buildPaymentOptions(),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOptions() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Payment Method',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),

          // Cash option
          _buildPaymentOption(
            icon: Icons.money,
            title: 'Cash',
            subtitle: 'Collect exact amount in cash',
            onTap: () => _selectPaymentMode('cash'),
            color: Colors.green,
          ),

          const SizedBox(height: 16),

          // QR Code option
          _buildPaymentOption(
            icon: Icons.qr_code,
            title: 'QR Code',
            subtitle: 'Generate QR for customer to scan',
            onTap: () => _selectPaymentMode('qr'),
            color: Colors.blue,
          ),

          const SizedBox(height: 16),

          // Cash + QR option
          _buildPaymentOption(
            icon: Icons.payment,
            title: 'Cash + QR Code',
            subtitle: 'Partial cash + QR for balance',
            onTap: () => _selectPaymentMode('cash_qr'),
            color: Colors.purple,
          ),

          const Spacer(),

          if (_paymentReceived)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.green),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: AppColors.green),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Payment received successfully!',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRCodeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const Text(
            'QR Code Payment',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),

          // QR Code
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // QR Code placeholder (since qr_flutter might not be available)
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code,
                        size: 80,
                        color: Colors.black54,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'QR Code',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  '₹${_qrAmount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Ask customer to scan this QR code',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Payment received button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _markPaymentReceived,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.green,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Text(
                'Payment Received',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
