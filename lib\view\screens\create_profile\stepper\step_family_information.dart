import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import '../../../widgets/common/common_elevated_button.dart';

class StepFamilyInformation extends StatefulWidget {
  final Function(Map<String, dynamic>) onContinue;

  const StepFamilyInformation({
    super.key,
    required this.onContinue,
  });

  @override
  State<StepFamilyInformation> createState() => _StepFamilyInformationState();
}

class _StepFamilyInformationState extends State<StepFamilyInformation> {
  final _formKey = GlobalKey<FormState>();
  final _spouseNameController = TextEditingController();
  final _spouseDobController = TextEditingController();
  
  List<Map<String, String>> _children = [];

  @override
  void dispose() {
    _spouseNameController.dispose();
    _spouseDobController.dispose();
    super.dispose();
  }

  void _addChild() {
    setState(() {
      _children.add({
        'name': '',
        'dob': '',
      });
    });
  }

  void _removeChild(int index) {
    setState(() {
      _children.removeAt(index);
    });
  }

  void _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 20)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.green,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.text = "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
    }
  }

  void _continue() {
    if (_formKey.currentState!.validate()) {
      final familyData = {
        'spouseName': _spouseNameController.text.trim(),
        'spouseDob': _spouseDobController.text.trim(),
        'children': _children,
      };
      
      widget.onContinue(familyData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Family information for Health Insurance',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    
                    // Spouse Information
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _spouseNameController,
                            decoration: const InputDecoration(
                              labelText: 'Spouse name',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 16,
                              ),
                            ),
                            validator: (value) {
                              if (value != null && value.isNotEmpty && value.length < 2) {
                                return 'Please enter a valid name';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _spouseDobController,
                            readOnly: true,
                            decoration: const InputDecoration(
                              labelText: 'DD-MM-YYYY',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 16,
                              ),
                              suffixIcon: Icon(Icons.calendar_today),
                            ),
                            onTap: () => _selectDate(_spouseDobController),
                            validator: (value) {
                              if (_spouseNameController.text.isNotEmpty && (value == null || value.isEmpty)) {
                                return 'Please select spouse DOB';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Children Section
                    ..._children.asMap().entries.map((entry) {
                      int index = entry.key;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Child name',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                ),
                                onChanged: (value) {
                                  _children[index]['name'] = value;
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter child name';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: TextFormField(
                                readOnly: true,
                                decoration: const InputDecoration(
                                  labelText: 'DD-MM-YYYY',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                  suffixIcon: Icon(Icons.calendar_today),
                                ),
                                onTap: () {
                                  showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now().subtract(const Duration(days: 365 * 5)),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime.now(),
                                    builder: (context, child) {
                                      return Theme(
                                        data: Theme.of(context).copyWith(
                                          colorScheme: Theme.of(context).colorScheme.copyWith(
                                            primary: AppColors.green,
                                          ),
                                        ),
                                        child: child!,
                                      );
                                    },
                                  ).then((picked) {
                                    if (picked != null) {
                                      setState(() {
                                        _children[index]['dob'] = "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
                                      });
                                    }
                                  });
                                },
                                controller: TextEditingController(text: _children[index]['dob']),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select child DOB';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _removeChild(index),
                              icon: const Icon(Icons.close, color: Colors.red),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    
                    // Add Child Button
                    GestureDetector(
                      onTap: _addChild,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.green, width: 2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.add, color: AppColors.green),
                            SizedBox(width: 8),
                            Text(
                              'Add',
                              style: TextStyle(
                                color: AppColors.green,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Continue Button
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: CommonElevatedButton(
                onPressed: _continue,
                child: const Text(
                  'Continue',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
