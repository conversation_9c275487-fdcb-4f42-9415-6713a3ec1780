import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:url_launcher/url_launcher.dart';

class FirstOrderDeliveryScreen extends StatefulWidget {
  const FirstOrderDeliveryScreen({super.key});

  @override
  State<FirstOrderDeliveryScreen> createState() => _FirstOrderDeliveryScreenState();
}

class _FirstOrderDeliveryScreenState extends State<FirstOrderDeliveryScreen> {
  late List<DeliveryOrderItem> _deliveryOrders;
  late int _currentOrderIndex;
  late DeliveryOrderItem _currentOrder;

  Timer? _deliveryTimer;
  int _remainingMinutes = 25; // Time pending to deliver
  bool _isEmergencyMode = false;
  bool _sosActivated = false;

  // MyGate/NoBrokerHood OTP
  final String _societyOTP = '4521';

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
    _startDeliveryTimer();
  }

  @override
  void dispose() {
    _deliveryTimer?.cancel();
    super.dispose();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _deliveryOrders = args?['deliveryOrders'] ?? [];
    _currentOrderIndex = args?['currentOrderIndex'] ?? 0;

    if (_deliveryOrders.isNotEmpty && _currentOrderIndex < _deliveryOrders.length) {
      _currentOrder = _deliveryOrders[_currentOrderIndex];
    } else {
      // Fallback order
      _currentOrder = const DeliveryOrderItem(
        orderId: 'E76592',
        customerName: 'John Doe',
        address: 'Sector 15, Vashi, Navi Mumbai',
        distance: 2.5,
        priority: 1,
        estimatedTime: '15 min',
        paymentMode: 'COD',
        bagCount: 3,
      );
    }
  }

  void _startDeliveryTimer() {
    _deliveryTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      setState(() {
        if (_remainingMinutes > 0) {
          _remainingMinutes--;
        } else {
          timer.cancel();
        }
      });
    });
  }

  void _toggleEmergency() {
    setState(() {
      _isEmergencyMode = !_isEmergencyMode;
    });

    if (_isEmergencyMode) {
      _showEmergencyDialog();
    }
  }

  void _activateSOS() {
    setState(() {
      _sosActivated = true;
    });

    _showSOSDialog();
  }

  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emergency Mode Activated'),
        content: const Text('Emergency services have been notified. Stay safe!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSOSDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SOS Activated'),
        content: const Text('Emergency contacts and support team have been alerted. Help is on the way!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _openGoogleMaps() async {
    final destination = Uri.encodeComponent(_currentOrder.address);
    final url = 'https://www.google.com/maps/dir/?api=1&destination=$destination';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open Google Maps')),
      );
    }
  }

  void _callCustomer() {
    // Implement call masking here
    final phoneNumber = 'tel:+919876543210'; // Masked number
    launchUrl(Uri.parse(phoneNumber));
  }

  void _reachedSocietyGate() {
    Get.toNamed(AppRoutes.reachedSocietyGate, arguments: {
      'currentOrder': _currentOrder,
      'societyOTP': _societyOTP,
    });
  }

  String _formatTime(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    if (hours > 0) {
      return '${hours}h ${mins}m';
    }
    return '${mins}m';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: Row(
          children: [
            Text(
              'Order ${_currentOrder.priority}',
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '#${_currentOrder.orderId}',
              style: const TextStyle(
                color: Colors.black54,
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        centerTitle: false,
        actions: [
          // Time pending
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _remainingMinutes <= 5 ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.timer,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_remainingMinutes),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Map section
          Expanded(
            flex: 3,
            child: _buildMapSection(),
          ),

          // Customer details section
          Expanded(
            flex: 2,
            child: _buildCustomerDetailsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildMapSection() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Color(0xFFE8F5E8),
      ),
      child: Stack(
        children: [
          // Map placeholder with route
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Delivery location pin
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 16),
                // Customer address
                Text(
                  _currentOrder.customerName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentOrder.address,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Emergency and SOS switches (top left)
          Positioned(
            top: 20,
            left: 20,
            child: Column(
              children: [
                // Emergency switch
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _isEmergencyMode ? Colors.red : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onTap: _toggleEmergency,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning,
                          color: _isEmergencyMode ? Colors.white : Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Emergency',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _isEmergencyMode ? Colors.white : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // SOS switch
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _sosActivated ? Colors.red : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onTap: _activateSOS,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.sos,
                          color: _sosActivated ? Colors.white : Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'SOS',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _sosActivated ? Colors.white : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Google Maps button (bottom right)
          Positioned(
            bottom: 20,
            right: 20,
            child: FloatingActionButton(
              onPressed: _openGoogleMaps,
              backgroundColor: Colors.blue,
              child: const Icon(
                Icons.map,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerDetailsSection() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Customer info header
          Row(
            children: [
              // Customer avatar
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  Icons.person,
                  color: AppColors.green,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // Customer details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentOrder.customerName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentOrder.address,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Call button
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.green,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: IconButton(
                  onPressed: _callCustomer,
                  icon: const Icon(
                    Icons.phone,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Order details row
          Row(
            children: [
              // Payment status
              Expanded(
                child: _buildInfoCard(
                  icon: Icons.payment,
                  title: 'Payment',
                  value: _currentOrder.paymentMode,
                  color: _currentOrder.paymentMode == 'COD' ? Colors.green : Colors.purple,
                ),
              ),

              const SizedBox(width: 12),

              // Delivery type
              Expanded(
                child: _buildInfoCard(
                  icon: Icons.local_shipping,
                  title: 'Delivery',
                  value: 'Standard',
                  color: Colors.blue,
                ),
              ),

              const SizedBox(width: 12),

              // Bag count
              Expanded(
                child: _buildInfoCard(
                  icon: Icons.shopping_bag,
                  title: 'Bags',
                  value: '${_currentOrder.bagCount}',
                  color: Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // MyGate/NoBrokerHood OTP
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.security,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Society Gate OTP',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _societyOTP,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                    letterSpacing: 4,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Show this OTP at society gate',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Reached society gate button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _reachedSocietyGate,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.green,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_city,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Reached Society Gate',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
