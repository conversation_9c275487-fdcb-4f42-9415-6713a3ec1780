import 'package:flutter/material.dart';

/// Common widget for displaying cached asset images with consistent behavior
/// This widget provides optimized asset image loading with caching capabilities
class CachedAssetImage extends StatelessWidget {
  /// The asset path (e.g., 'assets/icons/rain_icon.png')
  final String assetPath;

  /// Width of the image
  final double? width;

  /// Height of the image
  final double? height;

  /// How the image should be inscribed into the box
  final BoxFit? fit;

  /// Color to apply to the image
  final Color? color;

  /// Color blend mode
  final BlendMode? colorBlendMode;

  /// Alignment of the image within its bounds
  final AlignmentGeometry alignment;

  /// How to paint any portions of the box not covered by the image
  final ImageRepeat repeat;

  /// Whether to exclude this image from semantics
  final bool excludeFromSemantics;

  /// Semantic label for the image
  final String? semanticLabel;

  /// Widget to show while loading
  final Widget? placeholder;

  /// Widget to show on error
  final Widget? errorWidget;

  /// Custom error builder
  final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder;

  /// Whether to show loading indicator
  final bool showLoadingIndicator;

  /// Loading indicator color
  final Color? loadingIndicatorColor;

  /// Border radius for the image
  final BorderRadius? borderRadius;

  /// Box shadow for the image
  final List<BoxShadow>? boxShadow;

  /// Background color behind the image
  final Color? backgroundColor;

  /// Padding around the image
  final EdgeInsetsGeometry? padding;

  /// Margin around the image container
  final EdgeInsetsGeometry? margin;

  const CachedAssetImage({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.colorBlendMode,
    this.alignment = Alignment.center,
    this.repeat = ImageRepeat.noRepeat,
    this.excludeFromSemantics = false,
    this.semanticLabel,
    this.placeholder,
    this.errorWidget,
    this.errorBuilder,
    this.showLoadingIndicator = false,
    this.loadingIndicatorColor,
    this.borderRadius,
    this.boxShadow,
    this.backgroundColor,
    this.padding,
    this.margin,
  });

  /// Factory constructor for icons with common defaults
  factory CachedAssetImage.icon({
    required String assetPath,
    double size = 24.0,
    Color? color,
    BoxFit fit = BoxFit.contain,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return CachedAssetImage(
      assetPath: assetPath,
      width: size,
      height: size,
      fit: fit,
      color: color,
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  /// Factory constructor for avatars/profile images
  factory CachedAssetImage.avatar({
    required String assetPath,
    double size = 40.0,
    BoxFit fit = BoxFit.cover,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return CachedAssetImage(
      assetPath: assetPath,
      width: size,
      height: size,
      fit: fit,
      borderRadius: BorderRadius.circular(size / 2),
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  /// Factory constructor for banners/large images
  factory CachedAssetImage.banner({
    required String assetPath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return CachedAssetImage(
      assetPath: assetPath,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      showLoadingIndicator: true,
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if the asset is an SVG file
    final isSvg = assetPath.toLowerCase().endsWith('.svg');

    Widget imageWidget;

    if (isSvg) {
      // For SVG files, show a fallback icon with a warning
      imageWidget = Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: borderRadius,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: (width != null && height != null) ? (width! < height! ? width! * 0.4 : height! * 0.4) : 16,
              color: Colors.grey.shade400,
            ),
            if (width != null && height != null && width! > 40 && height! > 40) ...[
              const SizedBox(height: 4),
              Text(
                'SVG',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      );
    } else {
      // For raster images (PNG, JPG, etc.)
      imageWidget = Image.asset(
        assetPath,
        width: width,
        height: height,
        fit: fit,
        color: color,
        colorBlendMode: colorBlendMode,
        alignment: alignment,
        repeat: repeat,
        excludeFromSemantics: excludeFromSemantics,
        semanticLabel: semanticLabel,
        errorBuilder: errorBuilder ?? _defaultErrorBuilder,
        // Note: Asset images are automatically cached by Flutter
        // but we can add additional caching logic here if needed
      );
    }

    // Wrap with loading indicator if requested
    if (showLoadingIndicator) {
      imageWidget = Stack(
        alignment: Alignment.center,
        children: [
          if (placeholder != null)
            placeholder!
          else
            Container(
              width: width,
              height: height,
              color: backgroundColor ?? Colors.grey.shade200,
              child: Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      loadingIndicatorColor ?? Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ),
            ),
          imageWidget,
        ],
      );
    }

    // Apply border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    // Apply container styling if needed
    if (backgroundColor != null || boxShadow != null || padding != null || margin != null) {
      imageWidget = Container(
        width: width,
        height: height,
        margin: margin,
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius,
          boxShadow: boxShadow,
        ),
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Default error builder that shows a fallback icon
  Widget _defaultErrorBuilder(BuildContext context, Object error, StackTrace? stackTrace) {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: borderRadius,
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey.shade400,
        size: (width != null && height != null) ? (width! < height! ? width! * 0.5 : height! * 0.5) : 24,
      ),
    );
  }
}

/// Extension methods for common use cases
extension CachedAssetImageExtensions on CachedAssetImage {
  /// Add a circular border
  CachedAssetImage withCircularBorder({
    Color borderColor = Colors.white,
    double borderWidth = 2.0,
  }) {
    return CachedAssetImage(
      assetPath: assetPath,
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      alignment: alignment,
      repeat: repeat,
      excludeFromSemantics: excludeFromSemantics,
      semanticLabel: semanticLabel,
      placeholder: placeholder,
      errorWidget: errorWidget,
      errorBuilder: errorBuilder,
      showLoadingIndicator: showLoadingIndicator,
      loadingIndicatorColor: loadingIndicatorColor,
      borderRadius: borderRadius,
      boxShadow: [
        BoxShadow(
          color: borderColor,
          spreadRadius: borderWidth,
        ),
        ...?boxShadow,
      ],
      backgroundColor: backgroundColor,
      padding: padding,
      margin: margin,
    );
  }

  /// Add a shadow effect
  CachedAssetImage withShadow({
    Color shadowColor = Colors.black26,
    double blurRadius = 4.0,
    Offset offset = const Offset(0, 2),
  }) {
    return CachedAssetImage(
      assetPath: assetPath,
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      alignment: alignment,
      repeat: repeat,
      excludeFromSemantics: excludeFromSemantics,
      semanticLabel: semanticLabel,
      placeholder: placeholder,
      errorWidget: errorWidget,
      errorBuilder: errorBuilder,
      showLoadingIndicator: showLoadingIndicator,
      loadingIndicatorColor: loadingIndicatorColor,
      borderRadius: borderRadius,
      boxShadow: [
        BoxShadow(
          color: shadowColor,
          blurRadius: blurRadius,
          offset: offset,
        ),
        ...?boxShadow,
      ],
      backgroundColor: backgroundColor,
      padding: padding,
      margin: margin,
    );
  }
}
