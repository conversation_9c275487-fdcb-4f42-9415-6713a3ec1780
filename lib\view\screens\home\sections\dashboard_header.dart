import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/services/weather/weather_service.dart';
import 'package:kisankonnect_rider/controllers/weather_controller.dart';
import 'package:kisankonnect_rider/view/widgets/weather/rain_animation.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class DashboardHeader extends StatefulWidget {
  final bool isOnline;
  final ValueChanged<bool> onToggle;
  const DashboardHeader({required this.isOnline, required this.onToggle, super.key});

  @override
  State<DashboardHeader> createState() => _DashboardHeaderState();
}

class _DashboardHeaderState extends State<DashboardHeader> {
  final WeatherController _weatherController = Get.put(WeatherController());

  @override
  void initState() {
    super.initState();
    // For testing: simulate heavy rain after 3 seconds
    // Remove this in production
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _weatherController.simulateHeavyRain();
      }
    });
  }

  /// Make a phone call to the support number
  static Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        // Show error message if phone dialing is not available
        Get.snackbar(
          AppStrings.get('error'),
          AppStrings.get('phoneCallNotSupported'),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any errors
      Get.snackbar(
        AppStrings.get('error'),
        AppStrings.get('phoneCallFailed'),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Fixed header height - 64px (excluding safe area)
    const double headerHeight = 64.0;
    final horizontalPadding = ResponsiveUtils.spacingM(context);

    return Obx(() {
      // Weather-aware background - dramatic stormy gradient when raining
      Decoration backgroundDecoration;
      if (_weatherController.isHeavyRain || _weatherController.isRaining) {
        // Stormy gradient background matching the dramatic rain image
        backgroundDecoration = BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF9CA3AF), // Light gray clouds at top
              const Color(0xFF6B7280), // Medium gray
              const Color(0xFF4B5563), // Darker gray
              const Color(0xFF374151), // Dark stormy gray
              const Color(0xFF1F2937), // Very dark at bottom
            ],
            stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(30),
            bottomRight: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x14000000), // #00000014 - exact match
              offset: const Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        );
      } else {
        // Normal solid color background for clear weather
        backgroundDecoration = BoxDecoration(
          color: widget.isOnline ? AppColors.green : Colors.grey.shade600,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(30),
            bottomRight: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x14000000), // #00000014 - exact match
              offset: const Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        );
      }

      return Material(
        elevation: 0,
        child: Stack(
          children: [
            Container(
              decoration: backgroundDecoration,
              child: SafeArea(
                maintainBottomViewPadding: true,
                child: Container(
                  height: headerHeight, // Fixed 64px height
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding,
                    vertical: 8, // Reduced vertical padding
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: 4), // Reduced spacing for compact header
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: ResponsiveUtils.spacingS(context),
                              vertical: ResponsiveUtils.height(context, 0.8),
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(
                                  ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Status icon
                                Container(
                                  width: ResponsiveUtils.width(context, 2.5),
                                  height: ResponsiveUtils.width(context, 2.5),
                                  decoration: BoxDecoration(
                                    color: widget.isOnline ? Colors.white : Colors.grey.shade400,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                SizedBox(width: ResponsiveUtils.spacingS(context)),
                                // Status text
                                Text(
                                  widget.isOnline ? AppStrings.get('online') : AppStrings.get('offline'),
                                  style: AppTextTheme.buttonLarge.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: ResponsiveUtils.spacingS(context)),
                          // Custom toggle switch with symbols
                          GestureDetector(
                            onTap: () => widget.onToggle(!widget.isOnline),
                            child: Container(
                              width: 60,
                              height: 30,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                color: widget.isOnline ? Colors.white.withValues(alpha: 0.3) : Colors.black26,
                              ),
                              child: Stack(
                                children: [
                                  // Thumb container
                                  AnimatedPositioned(
                                    duration: Duration(milliseconds: 200),
                                    curve: Curves.easeInOut,
                                    left: widget.isOnline ? 30 : 2,
                                    top: 2,
                                    child: Container(
                                      width: 26,
                                      height: 26,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black12,
                                            blurRadius: 4,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: Text(
                                          widget.isOnline ? '✓' : '✕',
                                          style: TextStyle(
                                            color: widget.isOnline ? AppColors.green : Colors.grey,
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Rain indicator button - compact size (RIGHT side of toggle)
                          if (_weatherController.isRaining) ...[
                            SizedBox(width: 6), // Reduced spacing
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 6, // Reduced padding
                                vertical: 4, // Reduced padding
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(12), // Smaller radius
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.flash_on,
                                    color: Colors.white,
                                    size: 12, // Smaller icon
                                  ),
                                  SizedBox(width: 3), // Reduced spacing
                                  Text(
                                    "₹+25",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10, // Smaller font
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],

                          const Spacer(),
                          // Action buttons - compact size
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Wallet button - 24px
                              Container(
                                width: 32,
                                height: 32,
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  icon: Icon(
                                    Icons.account_balance_wallet_outlined,
                                    color: Colors.white,
                                    size: 24, // Fixed 24px
                                  ),
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.wallet);
                                  },
                                ),
                              ),
                              // Notification button - 24px
                              Container(
                                width: 32,
                                height: 32,
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  icon: Icon(
                                    Icons.notifications,
                                    color: Colors.white,
                                    size: 24, // Fixed 24px
                                  ),
                                  onPressed: () {},
                                ),
                              ),
                              // Support button - 24px
                              Container(
                                width: 32,
                                height: 32,
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  icon: Icon(
                                    Icons.headset_mic_outlined,
                                    color: Colors.white,
                                    size: 24, // Fixed 24px
                                  ),
                                  onPressed: () => _makePhoneCall('7499465693'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Rain animation overlay for heavy rain
            if (_weatherController.isHeavyRain)
              Positioned.fill(
                child: IgnorePointer(
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                    child: RainAnimation(
                      intensity: _weatherController.rainIntensity,
                      rainColor: Colors.white.withValues(alpha: 0.6),
                      width: MediaQuery.of(context).size.width,
                      height: headerHeight, // Fixed 64px height
                    ),
                  ),
                ),
              ),
            // Lightning effect for thunderstorms
            if (_weatherController.currentCondition == WeatherCondition.thunderstorm)
              Positioned.fill(
                child: IgnorePointer(
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                    child: LightningEffect(
                      isActive: true,
                      lightningColor: Colors.white,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
