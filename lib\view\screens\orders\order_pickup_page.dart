import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class OrderPickupPage extends StatefulWidget {
  const OrderPickupPage({super.key});

  @override
  State<OrderPickupPage> createState() => _OrderPickupPageState();
}

class _OrderPickupPageState extends State<OrderPickupPage> {
  late PickupSession _pickupSession;
  Timer? _timer;
  int _remainingSeconds = 45;
  bool _timerExpired = false;

  @override
  void initState() {
    super.initState();
    _initializePickupSession();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _initializePickupSession() {
    // Sample orders - in real app, this would come from API
    final orders = [
      const PickupOrderItem(
        orderId: 'E76592',
        columnNumber: 12,
        saddleBags: 3,
        silverBags: 2,
        milkPouches: 1,
      ),
      const PickupOrderItem(
        orderId: 'E76593',
        columnNumber: 24,
        saddleBags: 2,
        silverBags: 1,
        milkPouches: 0, // This order won't show up
      ),
      const PickupOrderItem(
        orderId: 'E76594',
        columnNumber: 25,
        saddleBags: 1,
        silverBags: 0,
        milkPouches: 2,
      ),
      const PickupOrderItem(
        orderId: 'E76595',
        columnNumber: 32,
        saddleBags: 4,
        silverBags: 3,
        milkPouches: 2,
      ),
      const PickupOrderItem(
        orderId: 'E76596',
        columnNumber: 39,
        saddleBags: 2,
        silverBags: 1,
        milkPouches: 1,
      ),
      const PickupOrderItem(
        orderId: 'E76597',
        columnNumber: 15,
        saddleBags: 1,
        silverBags: 0,
        milkPouches: 0, // This order won't show up
      ),
    ];

    _pickupSession = PickupSession(
      sessionId: 'pickup_${DateTime.now().millisecondsSinceEpoch}',
      orders: orders,
      startTime: DateTime.now(),
    );
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _timerExpired = true;
          timer.cancel();
        }
      });
    });
  }

  void _startScanning() {
    if (_timerExpired) {
      _showTimerExpiredDialog();
      return;
    }

    // Navigate to scanning screen
    Get.toNamed(AppRoutes.orderScanning, arguments: {
      'pickupSession': _pickupSession,
    });
  }

  void _showTimerExpiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Timer Expired'),
        content: const Text('The 45-second timer has expired. Please contact your supervisor.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              Get.back(); // Go back to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final displayableOrders = _pickupSession.displayableOrders;

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Order Pickup',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        actions: [
          // 45-second timer
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _timerExpired ? Colors.red : (_remainingSeconds <= 10 ? Colors.orange : AppColors.green),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _timerExpired ? Icons.timer_off : Icons.timer,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  _timerExpired ? 'EXPIRED' : _formatTime(_remainingSeconds),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Orders: ${displayableOrders.length}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Start scanning before timer expires',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Orders list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: displayableOrders.length,
              itemBuilder: (context, index) {
                final order = displayableOrders[index];
                return _buildOrderCard(order, index);
              },
            ),
          ),

          // Scan order button
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _timerExpired ? null : _startScanning,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _timerExpired ? Colors.grey : AppColors.green,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _timerExpired ? Icons.timer_off : Icons.qr_code_scanner,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _timerExpired ? 'Timer Expired' : 'Scan Orders',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(PickupOrderItem order, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order ID
          Text(
            'Order ID - #${order.orderId}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Dashed line
          _buildDashedLine(),
          const SizedBox(height: 12),

          // Order details row
          Row(
            children: [
              // Column number (red background)
              Expanded(
                child: _buildDetailItem(
                  label: 'Column',
                  value: order.columnNumber.toString(),
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                ),
              ),

              // Saddle bags
              if (order.saddleBags > 0)
                Expanded(
                  child: _buildDetailItem(
                    label: 'Saddle bag',
                    value: order.saddleBags.toString().padLeft(2, '0'),
                  ),
                ),

              // Silver bags (only show if > 0)
              if (order.silverBags > 0)
                Expanded(
                  child: _buildDetailItem(
                    label: 'Silver bag',
                    value: order.silverBags.toString().padLeft(2, '0'),
                  ),
                ),

              // Milk pouches (only show if > 0)
              if (order.milkPouches > 0)
                Expanded(
                  child: _buildDetailItem(
                    label: 'Milk pouch',
                    value: order.milkPouches.toString().padLeft(2, '0'),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required String label,
    required String value,
    Color? backgroundColor,
    Color? textColor,
  }) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              value,
              style: TextStyle(
                fontSize: backgroundColor != null ? 18 : 24,
                fontWeight: FontWeight.w600,
                color: textColor ?? Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDashedLine() {
    return Container(
      height: 1,
      child: Row(
        children: List.generate(
          40,
          (index) => Expanded(
            child: Container(
              height: 1,
              margin: const EdgeInsets.symmetric(horizontal: 0.5),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.grey.shade300 : Colors.transparent,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
