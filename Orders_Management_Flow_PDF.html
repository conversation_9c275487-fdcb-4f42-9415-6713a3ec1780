<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rider & FR App - Orders Management Flow</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2E7D32;
            font-size: 2.5em;
            margin: 0;
        }
        .header p {
            color: #666;
            font-size: 1.2em;
            margin: 10px 0 0 0;
        }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc h2 {
            color: #2E7D32;
            margin-top: 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .toc a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #2E7D32;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            font-size: 1.8em;
        }
        .section h3 {
            color: #388E3C;
            font-size: 1.4em;
            margin-top: 25px;
        }
        .flow-box {
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        .screen-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .screen-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .screen-card h4 {
            color: #2E7D32;
            margin-top: 0;
            font-size: 1.2em;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .payment-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .payment-card {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #FF9800;
        }
        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .api-table th,
        .api-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .api-table th {
            background: #4CAF50;
            color: white;
        }
        .api-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .highlight {
            background: #FFEB3B;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .status-flow {
            background: #E3F2FD;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196F3;
        }
        .exception-box {
            background: #FFEBEE;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #F44336;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rider & FR App</h1>
            <p>Orders Management Flow Documentation</p>
            <p style="font-size: 1em; color: #888;">Version 1.0 | Generated: December 2024</p>
        </div>

        <div class="toc">
            <h2>📋 Table of Contents</h2>
            <ul>
                <li><a href="#overview">1. Overview & Key Features</a></li>
                <li><a href="#lifecycle">2. Order Lifecycle & States</a></li>
                <li><a href="#flow">3. Complete Flow Diagram</a></li>
                <li><a href="#screens">4. Screen Details (12 Screens)</a></li>
                <li><a href="#payments">5. Payment Processing</a></li>
                <li><a href="#exceptions">6. Exception Handling</a></li>
                <li><a href="#technical">7. Technical Implementation</a></li>
                <li><a href="#apis">8. API Integration</a></li>
                <li><a href="#metrics">9. Performance Metrics</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2>🎯 1. Overview & Key Features</h2>
            <p>The Orders Management system provides a comprehensive workflow for handling orders from assignment to completion in the Rider & FR App.</p>
            
            <div class="flow-box">
                <h3>🚀 Key Features</h3>
                <div class="feature-list">
                    <ul>
                        <li><strong>Real-time Order Assignment:</strong> Automatic assignment when rider goes online</li>
                        <li><strong>QR Code Verification:</strong> Secure order pickup verification system</li>
                        <li><strong>Multiple Payment Modes:</strong> Cash, Online, QR Code, Partial payments</li>
                        <li><strong>Order Prioritization:</strong> Drag-and-drop order reordering capability</li>
                        <li><strong>Exception Handling:</strong> Customer unavailable, payment failures, cancellations</li>
                        <li><strong>Analytics Integration:</strong> Comprehensive tracking and reporting</li>
                        <li><strong>GPS Tracking:</strong> Real-time location and navigation</li>
                        <li><strong>Multi-language Support:</strong> Localized interface</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="lifecycle">
            <h2>🔄 2. Order Lifecycle & States</h2>
            
            <div class="status-flow">
                <h3>Order State Transitions</h3>
                <p style="font-size: 1.2em; text-align: center; font-weight: bold;">
                    <span class="highlight">Assigned</span> → 
                    <span class="highlight">Accepted</span> → 
                    <span class="highlight">Picked Up</span> → 
                    <span class="highlight">On The Way</span> → 
                    <span class="highlight">Delivered/Cancelled</span>
                </p>
            </div>

            <div class="screen-grid">
                <div class="screen-card">
                    <h4>📋 Assigned</h4>
                    <p>Order created by system and assigned to rider</p>
                    <ul>
                        <li>Notification sent to rider</li>
                        <li>Timer starts for acceptance</li>
                        <li>Order details displayed</li>
                    </ul>
                </div>
                <div class="screen-card">
                    <h4>✅ Accepted</h4>
                    <p>Rider confirms order acceptance</p>
                    <ul>
                        <li>Order added to active list</li>
                        <li>Pickup location shown</li>
                        <li>Navigation enabled</li>
                    </ul>
                </div>
                <div class="screen-card">
                    <h4>📦 Picked Up</h4>
                    <p>Order scanned and collected</p>
                    <ul>
                        <li>QR code verification</li>
                        <li>Items verified</li>
                        <li>Delivery route optimized</li>
                    </ul>
                </div>
                <div class="screen-card">
                    <h4>🚚 On The Way</h4>
                    <p>Delivery in progress</p>
                    <ul>
                        <li>GPS tracking active</li>
                        <li>Customer notifications sent</li>
                        <li>ETA calculated</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="flow">
            <h2>📊 3. Complete Flow Diagram</h2>
            <div class="flow-box">
                <h3>Main Order Flow</h3>
                <p><strong>Start:</strong> Rider App Launch → Status Check → Order Assignment</p>
                <p><strong>Pickup:</strong> Navigate → Scan QR → Verify Items → Mark Picked Up</p>
                <p><strong>Delivery:</strong> Prioritize Orders → Navigate → Customer Contact → Delivery</p>
                <p><strong>Payment:</strong> Process Payment → Verify → Complete</p>
                <p><strong>End:</strong> Order Summary → Next Order/Dashboard</p>
            </div>

            <div class="exception-box">
                <h3>🚨 Exception Flows</h3>
                <ul>
                    <li><strong>Customer Unavailable:</strong> Call → Wait → Neighbor → Return</li>
                    <li><strong>Payment Failure:</strong> Retry → Alternative Method → Manual Resolution</li>
                    <li><strong>Order Cancellation:</strong> Return to Warehouse → Update Status</li>
                </ul>
            </div>
        </div>

        <div class="section" id="screens">
            <h2>📱 4. Screen Details (12 Screens)</h2>
            
            <div class="screen-grid">
                <div class="screen-card">
                    <h4>1. Order Assignment Dialog</h4>
                    <p><strong>Purpose:</strong> Display assigned orders when rider goes online</p>
                    <div class="feature-list">
                        <ul>
                            <li>Total orders count</li>
                            <li>Expected earnings</li>
                            <li>Total distance</li>
                            <li>Bag information</li>
                            <li>Accept/Decline options</li>
                        </ul>
                    </div>
                </div>

                <div class="screen-card">
                    <h4>2. Pickup Order Screen</h4>
                    <p><strong>Purpose:</strong> Navigate to pickup location</p>
                    <div class="feature-list">
                        <ul>
                            <li>Warehouse location</li>
                            <li>Order details</li>
                            <li>Navigation integration</li>
                            <li>Contact information</li>
                        </ul>
                    </div>
                </div>

                <div class="screen-card">
                    <h4>3. Enhanced Pickup Order Screen</h4>
                    <p><strong>Purpose:</strong> Verify and collect orders</p>
                    <div class="feature-list">
                        <ul>
                            <li>QR code scanner</li>
                            <li>Order verification</li>
                            <li>Item checklist</li>
                            <li>Bag assignment</li>
                        </ul>
                    </div>
                </div>

                <div class="screen-card">
                    <h4>4. QR Scanner Screen</h4>
                    <p><strong>Purpose:</strong> Scan order QR codes</p>
                    <div class="feature-list">
                        <ul>
                            <li>Camera integration</li>
                            <li>QR code validation</li>
                            <li>Error handling</li>
                            <li>Manual entry option</li>
                        </ul>
                    </div>
                </div>

                <div class="screen-card">
                    <h4>5. Order Picked Up Screen</h4>
                    <p><strong>Purpose:</strong> Manage collected orders</p>
                    <div class="feature-list">
                        <ul>
                            <li>Drag-and-drop reordering</li>
                            <li>Priority management</li>
                            <li>Customer details</li>
                            <li>Start delivery option</li>
                        </ul>
                    </div>
                </div>

                <div class="screen-card">
                    <h4>6. On The Way Screen</h4>
                    <p><strong>Purpose:</strong> Track delivery progress</p>
                    <div class="feature-list">
                        <ul>
                            <li>GPS navigation</li>
                            <li>Customer contact</li>
                            <li>ETA calculation</li>
                            <li>Order details</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="payments">
            <h2>💳 5. Payment Processing</h2>
            
            <div class="payment-modes">
                <div class="payment-card">
                    <h4>💰 Cash/COD</h4>
                    <ul>
                        <li>Customer pays cash</li>
                        <li>Amount confirmation</li>
                        <li>Change calculation</li>
                        <li>Receipt generation</li>
                    </ul>
                </div>

                <div class="payment-card">
                    <h4>🌐 Online Payment</h4>
                    <ul>
                        <li>Payment status check</li>
                        <li>Transaction verification</li>
                        <li>Retry mechanisms</li>
                        <li>Failure handling</li>
                    </ul>
                </div>

                <div class="payment-card">
                    <h4>📱 QR Code</h4>
                    <ul>
                        <li>QR code display</li>
                        <li>Customer scanning</li>
                        <li>Payment verification</li>
                        <li>Transaction confirmation</li>
                    </ul>
                </div>

                <div class="payment-card">
                    <h4>🔄 Partial Payment</h4>
                    <ul>
                        <li>Cash portion collection</li>
                        <li>Digital portion processing</li>
                        <li>Combined verification</li>
                        <li>Total calculation</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="exceptions">
            <h2>⚠️ 6. Exception Handling</h2>
            
            <div class="exception-box">
                <h3>👤 Customer Unavailable</h3>
                <ul>
                    <li><strong>Call Again:</strong> Attempt phone contact with call tracking</li>
                    <li><strong>Wait & Return:</strong> Schedule redelivery with time slots</li>
                    <li><strong>Leave with Neighbor:</strong> Neighbor delivery with verification</li>
                    <li><strong>Return to Warehouse:</strong> Cancel and return items</li>
                </ul>
            </div>

            <div class="exception-box">
                <h3>💳 Payment Failures</h3>
                <ul>
                    <li><strong>Retry Payment:</strong> Multiple retry attempts with different methods</li>
                    <li><strong>Alternative Method:</strong> Switch between payment modes</li>
                    <li><strong>Manual Resolution:</strong> Contact support for assistance</li>
                    <li><strong>Partial Completion:</strong> Handle partial payment scenarios</li>
                </ul>
            </div>
        </div>

        <div class="section" id="technical">
            <h2>⚙️ 7. Technical Implementation</h2>
            
            <table class="api-table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Purpose</th>
                        <th>Key Features</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>OrderManagementController</strong></td>
                        <td>Core order operations</td>
                        <td>Accept, pickup, deliver, cancel orders</td>
                    </tr>
                    <tr>
                        <td><strong>RiderStatusController</strong></td>
                        <td>Rider status management</td>
                        <td>Online/offline status, order assignment</td>
                    </tr>
                    <tr>
                        <td><strong>PaymentService</strong></td>
                        <td>Payment processing</td>
                        <td>Multiple payment modes, verification</td>
                    </tr>
                    <tr>
                        <td><strong>LocationService</strong></td>
                        <td>GPS and navigation</td>
                        <td>Real-time tracking, route optimization</td>
                    </tr>
                    <tr>
                        <td><strong>NotificationService</strong></td>
                        <td>Push notifications</td>
                        <td>Order updates, customer notifications</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section" id="apis">
            <h2>🔌 8. API Integration</h2>
            
            <table class="api-table">
                <thead>
                    <tr>
                        <th>API Endpoint</th>
                        <th>Method</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>/orders/assigned</td>
                        <td>GET</td>
                        <td>Get assigned orders for rider</td>
                    </tr>
                    <tr>
                        <td>/orders/accept</td>
                        <td>POST</td>
                        <td>Accept assigned order</td>
                    </tr>
                    <tr>
                        <td>/orders/pickup</td>
                        <td>POST</td>
                        <td>Mark order as picked up</td>
                    </tr>
                    <tr>
                        <td>/orders/deliver</td>
                        <td>POST</td>
                        <td>Complete order delivery</td>
                    </tr>
                    <tr>
                        <td>/payments/verify</td>
                        <td>POST</td>
                        <td>Verify payment status</td>
                    </tr>
                    <tr>
                        <td>/tracking/location</td>
                        <td>POST</td>
                        <td>Update rider location</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section" id="metrics">
            <h2>📈 9. Performance Metrics</h2>
            
            <div class="flow-box">
                <h3>Key Performance Indicators (KPIs)</h3>
                <ul>
                    <li><strong>Order Acceptance Rate:</strong> Percentage of orders accepted by riders</li>
                    <li><strong>Delivery Success Rate:</strong> Successful deliveries vs total attempts</li>
                    <li><strong>Average Delivery Time:</strong> Time from pickup to delivery completion</li>
                    <li><strong>Payment Success Rate:</strong> Successful payment processing percentage</li>
                    <li><strong>Customer Satisfaction:</strong> Customer feedback and rating scores</li>
                    <li><strong>Exception Rate:</strong> Frequency of delivery exceptions</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3>Analytics Tracking</h3>
                <ul>
                    <li>Order lifecycle event tracking</li>
                    <li>Payment method usage statistics</li>
                    <li>Exception occurrence rates and patterns</li>
                    <li>Rider performance metrics and rankings</li>
                    <li>Customer interaction data and feedback</li>
                    <li>Route optimization effectiveness</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>📄 Document Information</strong></p>
            <p>This comprehensive documentation covers the complete Orders Management Flow in the Rider & FR App.</p>
            <p><em>For technical implementation details and code references, please refer to the application codebase.</em></p>
            <p style="color: #666; font-size: 0.9em;">Generated: December 2024 | Version: 1.0</p>
        </div>
    </div>
</body>
</html>
