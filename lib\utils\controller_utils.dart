import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../controllers/order_management_controller.dart';

/// Utility class for safely accessing controllers
class ControllerUtils {
  /// Safely get OrderManagementController with fallback creation
  static OrderManagementController getOrderManagementController() {
    try {
      return Get.find<OrderManagementController>();
    } catch (e) {
      debugPrint('OrderManagementController not found, creating new instance: $e');
      return Get.put(OrderManagementController(), permanent: true);
    }
  }

  /// Check if OrderManagementController is registered
  static bool isOrderManagementControllerRegistered() {
    return Get.isRegistered<OrderManagementController>();
  }

  /// Force initialize OrderManagementController
  static OrderManagementController forceInitializeOrderManagementController() {
    try {
      // Try to delete existing instance if any
      if (Get.isRegistered<OrderManagementController>()) {
        Get.delete<OrderManagementController>(force: true);
      }
    } catch (e) {
      debugPrint('Error deleting existing OrderManagementController: $e');
    }
    
    // Create new instance
    return Get.put(OrderManagementController(), permanent: true);
  }

  /// Safely get any controller with fallback
  static T safeGetController<T>({
    required T Function() fallbackCreator,
    bool permanent = false,
  }) {
    try {
      return Get.find<T>();
    } catch (e) {
      debugPrint('Controller ${T.toString()} not found, creating new instance: $e');
      return Get.put(fallbackCreator(), permanent: permanent);
    }
  }
}
