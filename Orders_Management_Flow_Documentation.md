# Rider & FR App - Orders Management Flow Documentation

## Table of Contents
1. [Overview](#overview)
2. [Order Lifecycle](#order-lifecycle)
3. [Flow Diagrams](#flow-diagrams)
4. [Screen Details](#screen-details)
5. [Payment Processing](#payment-processing)
6. [Exception Handling](#exception-handling)
7. [Technical Implementation](#technical-implementation)
8. [API Integration](#api-integration)

---

## Overview

The Orders Management system in the Rider & FR App provides a comprehensive workflow for order handling from assignment to completion. The system supports multiple payment modes, real-time tracking, and robust exception handling.

### Key Features
- **Real-time Order Assignment**: Automatic order assignment when rider goes online
- **QR Code Verification**: Secure order pickup verification
- **Multiple Payment Modes**: Cash, Online, QR Code, Partial payments
- **Order Prioritization**: Drag-and-drop order reordering
- **Exception Handling**: Customer unavailable, payment failures, cancellations
- **Analytics Integration**: Comprehensive tracking and reporting

---

## Order Lifecycle

### 1. Order States
```
Assigned → Accepted → Picked Up → On The Way → Delivered/Cancelled
```

### 2. State Transitions
- **Assigned**: Order created by system and assigned to rider
- **Accepted**: Rider confirms order acceptance
- **Picked Up**: Order scanned and collected from warehouse
- **On The Way**: Delivery in progress with GPS tracking
- **Delivered**: Successful delivery with payment completion
- **Cancelled**: Order cancelled due to various reasons

### 3. Payment Modes Supported
- **Cash/COD**: Traditional cash on delivery
- **Online**: Digital payment verification
- **QR Code**: QR-based payment scanning
- **Partial**: Mixed payment (cash + digital)

---

## Flow Diagrams

### Main Order Flow
```
Rider Launch → Status Check → Order Assignment → Pickup → Delivery → Payment → Completion
```

### Exception Flows
- **Customer Unavailable**: Call → Wait → Neighbor → Return
- **Payment Failure**: Retry → Alternative Method → Manual Resolution
- **Order Cancellation**: Return to Warehouse → Update Status

---

## Screen Details

### 1. Order Assignment Dialog
- **Purpose**: Display assigned orders when rider goes online
- **Features**: 
  - Total orders count
  - Expected earnings
  - Total distance
  - Bag information (saddle bags, silver bags)
  - Accept/Decline options

### 2. Pickup Order Screen
- **Purpose**: Navigate to pickup location
- **Features**:
  - Warehouse location
  - Order details
  - Navigation integration
  - Contact information

### 3. Enhanced Pickup Order Screen
- **Purpose**: Verify and collect orders
- **Features**:
  - QR code scanner
  - Order verification
  - Item checklist
  - Bag assignment

### 4. QR Scanner Screen
- **Purpose**: Scan order QR codes for verification
- **Features**:
  - Camera integration
  - QR code validation
  - Error handling
  - Manual entry option

### 5. Order Picked Up Screen
- **Purpose**: Manage collected orders
- **Features**:
  - Order list with drag-and-drop
  - Priority reordering
  - Customer details
  - Start delivery option

### 6. On The Way Screen
- **Purpose**: Track delivery progress
- **Features**:
  - GPS navigation
  - Customer contact
  - ETA calculation
  - Order details

### 7. Delivery Completion Screen
- **Purpose**: Complete order delivery
- **Features**:
  - Customer verification
  - Payment processing
  - Photo capture
  - Signature collection

### 8. COD Payment Screen
- **Purpose**: Handle cash payments
- **Features**:
  - Amount calculation
  - Change calculation
  - Payment confirmation
  - Receipt generation

### 9. Customer Unavailable Screen
- **Purpose**: Handle delivery exceptions
- **Features**:
  - Action selection (Call, Wait, Neighbor, Return)
  - Contact attempts tracking
  - Rescheduling options
  - Documentation

### 10. Online Payment Verification Screen
- **Purpose**: Verify digital payments
- **Features**:
  - Payment status check
  - Retry mechanisms
  - Alternative payment options
  - Transaction details

### 11. OTP Verification Screen
- **Purpose**: Verify delivery with OTP
- **Features**:
  - OTP input
  - SMS/WhatsApp integration
  - Resend options
  - Manual verification

### 12. Order Summary Screen
- **Purpose**: Display completion summary
- **Features**:
  - Earnings breakdown
  - Delivery statistics
  - Next order options
  - Performance metrics

---

## Payment Processing

### Cash/COD Flow
1. Customer pays cash
2. Rider confirms amount
3. Change calculated if needed
4. Payment marked complete
5. Receipt generated

### Online Payment Flow
1. Check payment status
2. Verify transaction
3. Handle failures with retry
4. Confirm completion
5. Update order status

### QR Code Payment Flow
1. Display QR code
2. Customer scans and pays
3. Verify payment receipt
4. Confirm transaction
5. Complete order

### Partial Payment Flow
1. Collect cash portion
2. Process digital portion
3. Verify both payments
4. Calculate totals
5. Complete transaction

---

## Exception Handling

### Customer Unavailable
- **Call Again**: Attempt phone contact
- **Wait & Return**: Schedule redelivery
- **Leave with Neighbor**: Neighbor delivery with verification
- **Return to Warehouse**: Cancel and return items

### Payment Failures
- **Retry Payment**: Multiple retry attempts
- **Alternative Method**: Switch payment modes
- **Manual Resolution**: Contact support
- **Partial Completion**: Handle partial payments

### Order Cancellations
- **Customer Request**: Process cancellation
- **System Issues**: Technical cancellation
- **Rider Issues**: Rider-initiated cancellation
- **Return Process**: Return items to warehouse

---

## Technical Implementation

### Controllers
- **OrderManagementController**: Core order operations
- **RiderStatusController**: Rider online/offline status
- **ShiftController**: Shift management

### Models
- **Order**: Order data structure
- **Customer**: Customer information
- **Payment**: Payment details
- **DeliveryBagInfo**: Bag and item information

### Services
- **ApiService**: Backend communication
- **LocationService**: GPS and navigation
- **NotificationService**: Push notifications
- **PaymentService**: Payment processing

### Storage
- **SecureStorageService**: Secure data storage
- **Local Database**: Offline data management
- **Cache Management**: Performance optimization

---

## API Integration

### Order APIs
- `GET /orders/assigned` - Get assigned orders
- `POST /orders/accept` - Accept order
- `POST /orders/pickup` - Mark order picked up
- `POST /orders/deliver` - Complete delivery
- `POST /orders/cancel` - Cancel order

### Payment APIs
- `POST /payments/verify` - Verify payment
- `POST /payments/process` - Process payment
- `GET /payments/status` - Check payment status

### Tracking APIs
- `POST /tracking/location` - Update rider location
- `GET /tracking/route` - Get delivery route
- `POST /tracking/status` - Update delivery status

### Notification APIs
- `POST /notifications/send` - Send notifications
- `POST /notifications/whatsapp` - WhatsApp notifications
- `POST /notifications/sms` - SMS notifications

---

## Performance Metrics

### Key Performance Indicators (KPIs)
- **Order Acceptance Rate**: Percentage of orders accepted
- **Delivery Success Rate**: Successful deliveries vs total attempts
- **Average Delivery Time**: Time from pickup to delivery
- **Payment Success Rate**: Successful payment processing
- **Customer Satisfaction**: Customer feedback scores

### Analytics Tracking
- Order lifecycle events
- Payment method usage
- Exception occurrence rates
- Rider performance metrics
- Customer interaction data

---

## Security Considerations

### Data Protection
- Encrypted data transmission
- Secure payment processing
- Customer data privacy
- Order information security

### Authentication
- Rider authentication
- Order verification
- Payment authorization
- OTP validation

### Compliance
- PCI DSS compliance for payments
- GDPR compliance for data
- Local regulations adherence
- Security audit compliance

---

*This documentation provides a comprehensive overview of the Orders Management Flow in the Rider & FR App. For technical implementation details, refer to the codebase and API documentation.*
