import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:image_picker/image_picker.dart';

class DeliveryCompletionScreen extends StatefulWidget {
  const DeliveryCompletionScreen({super.key});

  @override
  State<DeliveryCompletionScreen> createState() => _DeliveryCompletionScreenState();
}

class _DeliveryCompletionScreenState extends State<DeliveryCompletionScreen> {
  late DeliveryOrderItem _currentOrder;
  late String _paymentMode;
  late double _totalAmount;

  File? _deliveryPhoto;
  final ImagePicker _picker = ImagePicker();

  bool _isProcessing = false;
  bool _hasFragileItems = false;
  bool _hasGiftItems = false;

  // Sample order items for missing/quality issues
  final List<OrderProduct> _orderProducts = [
    const OrderProduct(id: '1', name: 'Milk 1L', quantity: 2, category: 'Dairy'),
    const OrderProduct(id: '2', name: 'Bread', quantity: 1, category: 'Bakery'),
    const OrderProduct(id: '3', name: 'Eggs 12pc', quantity: 1, category: 'Dairy'),
    const OrderProduct(id: '4', name: 'Apples 1kg', quantity: 1, category: 'Fruits'),
    const OrderProduct(id: '5', name: 'Rice 5kg', quantity: 1, category: 'Grains'),
  ];

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _currentOrder = args?['currentOrder'] ??
        const DeliveryOrderItem(
          orderId: 'E76592',
          customerName: 'John Doe',
          address: 'Sector 15, Vashi, Navi Mumbai',
          distance: 2.5,
          priority: 1,
          estimatedTime: '15 min',
          paymentMode: 'COD',
          bagCount: 3,
        );

    _paymentMode = args?['paymentMode'] ?? 'cash';
    _totalAmount = args?['totalAmount'] ?? 450.0;

    // Check for fragile/gift items (sample logic)
    _hasFragileItems = _orderProducts.any((p) => p.category == 'Dairy');
    _hasGiftItems = _orderProducts.any((p) => p.name.toLowerCase().contains('gift'));
  }

  Future<void> _takeDeliveryPhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (photo != null) {
        setState(() {
          _deliveryPhoto = File(photo.path);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error taking photo: $e')),
      );
    }
  }

  void _showMissingItemsDialog() {
    showDialog(
      context: context,
      builder: (context) => _MissingItemsDialog(
        products: _orderProducts,
        onConfirm: (missingItems) {
          // Handle missing items
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${missingItems.length} items marked as missing')),
          );
        },
      ),
    );
  }

  void _showQualityIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => _QualityIssueDialog(
        products: _orderProducts,
        onConfirm: (qualityIssues) {
          // Handle quality issues
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${qualityIssues.length} items marked with quality issues')),
          );
        },
      ),
    );
  }

  void _showSwitchOrderDialog() {
    showDialog(
      context: context,
      builder: (context) => _SwitchOrderDialog(
        onConfirm: (reason) {
          // Handle switch order
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Order switched to last: $reason')),
          );
        },
      ),
    );
  }

  void _showReturnDeliveryDialog() {
    showDialog(
      context: context,
      builder: (context) => _ReturnDeliveryDialog(
        onConfirm: (reason, otp, chillpadCount) {
          // Handle return delivery
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Order returned: $reason')),
          );
        },
      ),
    );
  }

  void _markDeliveryComplete() {
    if (_deliveryPhoto == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please take a photo for delivery')),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    // Simulate processing
    Future.delayed(const Duration(seconds: 2), () {
      // Get.toNamed(AppRoutes.otpVerification, arguments: {
      //   'currentOrder': _currentOrder,
      //   'deliveryPhoto': _deliveryPhoto,
      // });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Delivery #${_currentOrder.orderId}',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Customer info header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.person,
                    color: AppColors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentOrder.customerName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentOrder.address,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '₹${_totalAmount.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.green,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Special items notice
          if (_hasFragileItems || _hasGiftItems)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Special Items',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (_hasFragileItems)
                    const Text(
                      '• Handle fragile items with care',
                      style: TextStyle(fontSize: 12, color: Colors.orange),
                    ),
                  if (_hasGiftItems)
                    const Text(
                      '• Gift items - ensure proper presentation',
                      style: TextStyle(fontSize: 12, color: Colors.orange),
                    ),
                ],
              ),
            ),

          // Action buttons
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Take photo section
                  _buildActionCard(
                    icon: Icons.camera_alt,
                    title: 'Take Photo for Delivery',
                    subtitle: _deliveryPhoto != null ? 'Photo captured ✓' : 'Required before marking delivery complete',
                    onTap: _takeDeliveryPhoto,
                    color: _deliveryPhoto != null ? AppColors.green : Colors.blue,
                    isCompleted: _deliveryPhoto != null,
                  ),

                  const SizedBox(height: 12),

                  // Missing items
                  _buildActionCard(
                    icon: Icons.inventory_2,
                    title: 'Items Missing',
                    subtitle: 'Mark products that are missing from order',
                    onTap: _showMissingItemsDialog,
                    color: Colors.red,
                  ),

                  const SizedBox(height: 12),

                  // Quality issues
                  _buildActionCard(
                    icon: Icons.report_problem,
                    title: 'Quality Issues',
                    subtitle: 'Report products with quality problems',
                    onTap: _showQualityIssueDialog,
                    color: Colors.orange,
                  ),

                  const SizedBox(height: 12),

                  // Switch order
                  _buildActionCard(
                    icon: Icons.swap_horiz,
                    title: 'Switch Order',
                    subtitle: 'Move this order to last in sequence',
                    onTap: _showSwitchOrderDialog,
                    color: Colors.purple,
                  ),

                  const SizedBox(height: 12),

                  // Return delivery
                  _buildActionCard(
                    icon: Icons.keyboard_return,
                    title: 'Return Delivery',
                    subtitle: 'Return order to warehouse',
                    onTap: _showReturnDeliveryDialog,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),

          // Delivery done button
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _markDeliveryComplete,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _deliveryPhoto != null ? AppColors.green : Colors.grey,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: _isProcessing
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Processing...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Delivery Done',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
    bool isCompleted = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isCompleted ? AppColors.green : Colors.grey.shade300,
            width: isCompleted ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                isCompleted ? Icons.check : icon,
                color: isCompleted ? AppColors.green : color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isCompleted ? AppColors.green : Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

// Dialog widgets
class _MissingItemsDialog extends StatefulWidget {
  final List<OrderProduct> products;
  final Function(List<OrderProduct>) onConfirm;

  const _MissingItemsDialog({
    required this.products,
    required this.onConfirm,
  });

  @override
  State<_MissingItemsDialog> createState() => _MissingItemsDialogState();
}

class _MissingItemsDialogState extends State<_MissingItemsDialog> {
  final Set<String> _selectedItems = {};

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Missing Items'),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: widget.products.length,
          itemBuilder: (context, index) {
            final product = widget.products[index];
            return CheckboxListTile(
              title: Text(product.name),
              subtitle: Text('Qty: ${product.quantity}'),
              value: _selectedItems.contains(product.id),
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _selectedItems.add(product.id);
                  } else {
                    _selectedItems.remove(product.id);
                  }
                });
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final missingItems = widget.products.where((p) => _selectedItems.contains(p.id)).toList();
            widget.onConfirm(missingItems);
            Get.back();
          },
          style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
          child: const Text('Confirm', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}

class _QualityIssueDialog extends StatefulWidget {
  final List<OrderProduct> products;
  final Function(List<OrderProduct>) onConfirm;

  const _QualityIssueDialog({
    required this.products,
    required this.onConfirm,
  });

  @override
  State<_QualityIssueDialog> createState() => _QualityIssueDialogState();
}

class _QualityIssueDialogState extends State<_QualityIssueDialog> {
  final Set<String> _selectedItems = {};

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Quality Issues'),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: widget.products.length,
          itemBuilder: (context, index) {
            final product = widget.products[index];
            return CheckboxListTile(
              title: Text(product.name),
              subtitle: Text('Qty: ${product.quantity}'),
              value: _selectedItems.contains(product.id),
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _selectedItems.add(product.id);
                  } else {
                    _selectedItems.remove(product.id);
                  }
                });
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final qualityIssues = widget.products.where((p) => _selectedItems.contains(p.id)).toList();
            widget.onConfirm(qualityIssues);
            Get.back();
          },
          style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
          child: const Text('Confirm', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}

class _SwitchOrderDialog extends StatefulWidget {
  final Function(String) onConfirm;

  const _SwitchOrderDialog({required this.onConfirm});

  @override
  State<_SwitchOrderDialog> createState() => _SwitchOrderDialogState();
}

class _SwitchOrderDialogState extends State<_SwitchOrderDialog> {
  String _selectedReason = '';
  final List<String> _reasons = [
    'Customer not available',
    'Address not found',
    'Customer requested later delivery',
    'Traffic/route issues',
    'Other',
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Switch Order'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Why do you want to switch this order to last?'),
          const SizedBox(height: 16),
          ...(_reasons.map((reason) => RadioListTile<String>(
                title: Text(reason),
                value: reason,
                groupValue: _selectedReason,
                onChanged: (value) {
                  setState(() {
                    _selectedReason = value ?? '';
                  });
                },
              ))),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedReason.isNotEmpty
              ? () {
                  widget.onConfirm(_selectedReason);
                  Get.back();
                }
              : null,
          style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
          child: const Text('Confirm', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}

class _ReturnDeliveryDialog extends StatefulWidget {
  final Function(String, String, int) onConfirm;

  const _ReturnDeliveryDialog({required this.onConfirm});

  @override
  State<_ReturnDeliveryDialog> createState() => _ReturnDeliveryDialogState();
}

class _ReturnDeliveryDialogState extends State<_ReturnDeliveryDialog> {
  String _selectedReason = '';
  final TextEditingController _otpController = TextEditingController();
  final TextEditingController _chillpadController = TextEditingController();

  final List<String> _reasons = [
    'Customer refused delivery',
    'Wrong address',
    'Customer not available after multiple attempts',
    'Payment issues',
    'Product quality issues',
    'Other',
  ];

  @override
  void dispose() {
    _otpController.dispose();
    _chillpadController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Return Delivery'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Reason for return:'),
            const SizedBox(height: 8),
            ...(_reasons.map((reason) => RadioListTile<String>(
                  title: Text(reason, style: const TextStyle(fontSize: 14)),
                  value: reason,
                  groupValue: _selectedReason,
                  onChanged: (value) {
                    setState(() {
                      _selectedReason = value ?? '';
                    });
                  },
                ))),
            const SizedBox(height: 16),
            TextField(
              controller: _otpController,
              decoration: const InputDecoration(
                labelText: 'Return OTP',
                border: OutlineInputBorder(),
                hintText: 'Enter 4-digit OTP',
              ),
              keyboardType: TextInputType.number,
              maxLength: 4,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _chillpadController,
              decoration: const InputDecoration(
                labelText: 'Chillpad Count',
                border: OutlineInputBorder(),
                hintText: 'Number of chillpads to return',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed:
              _selectedReason.isNotEmpty && _otpController.text.length == 4 && _chillpadController.text.isNotEmpty
                  ? () {
                      widget.onConfirm(
                        _selectedReason,
                        _otpController.text,
                        int.tryParse(_chillpadController.text) ?? 0,
                      );
                      Get.back();
                    }
                  : null,
          style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
          child: const Text('Confirm Return', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}
