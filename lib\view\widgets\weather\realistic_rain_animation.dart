import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Realistic rain animation that matches the dramatic stormy atmosphere
class RealisticRainAnimation extends StatefulWidget {
  final bool isHeavyRain;
  final double intensity; // 0.0 to 1.0

  const RealisticRainAnimation({
    super.key,
    this.isHeavyRain = false,
    this.intensity = 0.7,
  });

  @override
  State<RealisticRainAnimation> createState() => _RealisticRainAnimationState();
}

class _RealisticRainAnimationState extends State<RealisticRainAnimation>
    with TickerProviderStateMixin {
  late AnimationController _rainController;
  late AnimationController _windController;
  late List<RainDrop> _rainDrops;
  final math.Random _random = math.Random();

  @override
  void initState() {
    super.initState();
    
    // Main rain animation
    _rainController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    // Wind sway animation
    _windController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _initializeRainDrops();
    _startAnimations();
  }

  void _initializeRainDrops() {
    final dropCount = widget.isHeavyRain ? 150 : 100;
    _rainDrops = List.generate(dropCount, (index) => _createRandomRainDrop());
  }

  RainDrop _createRandomRainDrop() {
    return RainDrop(
      x: _random.nextDouble(),
      y: _random.nextDouble() * -0.5, // Start above screen
      speed: 0.02 + _random.nextDouble() * 0.03, // Varying speeds
      length: widget.isHeavyRain ? 15 + _random.nextDouble() * 25 : 10 + _random.nextDouble() * 15,
      opacity: 0.3 + _random.nextDouble() * 0.7,
      thickness: widget.isHeavyRain ? 1.5 + _random.nextDouble() * 1.5 : 1.0 + _random.nextDouble() * 1.0,
      windOffset: _random.nextDouble() * 0.02 - 0.01, // Slight wind variation
    );
  }

  void _startAnimations() {
    _rainController.addListener(() {
      setState(() {
        for (var drop in _rainDrops) {
          // Update rain drop position
          drop.y += drop.speed * widget.intensity;
          
          // Add wind effect
          drop.x += drop.windOffset * math.sin(_windController.value * 2 * math.pi);
          
          // Reset drop when it goes off screen
          if (drop.y > 1.2) {
            drop.y = _random.nextDouble() * -0.3;
            drop.x = _random.nextDouble();
          }
          
          // Keep drops within horizontal bounds
          if (drop.x < -0.1) drop.x = 1.1;
          if (drop.x > 1.1) drop.x = -0.1;
        }
      });
    });
    
    _rainController.repeat();
    _windController.repeat();
  }

  @override
  void dispose() {
    _rainController.dispose();
    _windController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: RainPainter(_rainDrops, widget.intensity),
      size: Size.infinite,
    );
  }
}

class RainDrop {
  double x;
  double y;
  final double speed;
  final double length;
  final double opacity;
  final double thickness;
  final double windOffset;

  RainDrop({
    required this.x,
    required this.y,
    required this.speed,
    required this.length,
    required this.opacity,
    required this.thickness,
    required this.windOffset,
  });
}

class RainPainter extends CustomPainter {
  final List<RainDrop> rainDrops;
  final double intensity;

  RainPainter(this.rainDrops, this.intensity);

  @override
  void paint(Canvas canvas, Size size) {
    for (var drop in rainDrops) {
      final paint = Paint()
        ..color = Color.lerp(
          const Color(0x40FFFFFF), // Light rain
          const Color(0x80FFFFFF), // Heavy rain
          intensity,
        )!.withValues(alpha: drop.opacity)
        ..strokeWidth = drop.thickness
        ..strokeCap = StrokeCap.round;

      final startX = drop.x * size.width;
      final startY = drop.y * size.height;
      final endX = startX + (drop.length * 0.3); // Slight angle for realism
      final endY = startY + drop.length;

      // Draw rain drop as a line
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );

      // Add subtle glow effect for heavy rain
      if (intensity > 0.7) {
        final glowPaint = Paint()
          ..color = const Color(0x20FFFFFF)
          ..strokeWidth = drop.thickness + 1
          ..strokeCap = StrokeCap.round;
        
        canvas.drawLine(
          Offset(startX, startY),
          Offset(endX, endY),
          glowPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
