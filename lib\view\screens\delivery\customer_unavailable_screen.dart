import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/controllers/order_management_controller.dart';
import 'package:kisankonnect_rider/models/order_models.dart';
import 'package:kisankonnect_rider/mixins/analytics_mixin.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/utils/controller_utils.dart';
import '../../widgets/common/common_app_bar.dart';

class CustomerUnavailableScreen extends StatefulWidget {
  final String orderId;
  final String customerName;
  final String customerPhone;
  final String customerAddress;
  final Order? order;

  const CustomerUnavailableScreen({
    super.key,
    required this.orderId,
    required this.customerName,
    required this.customerPhone,
    required this.customerAddress,
    this.order,
  });

  @override
  State<CustomerUnavailableScreen> createState() => _CustomerUnavailableScreenState();
}

class _CustomerUnavailableScreenState extends State<CustomerUnavailableScreen> with AnalyticsMixin {
  OrderManagementController? _orderController;
  final TextEditingController _notesController = TextEditingController();

  bool _isProcessing = false;
  String _selectedAction = '';
  int _callAttempts = 0;
  bool _hasCalledCustomer = false;

  final List<Map<String, dynamic>> _actionOptions = [
    {
      'id': 'call_again',
      'title': 'Call Customer Again',
      'subtitle': 'Try calling the customer one more time',
      'icon': Icons.phone,
      'color': Colors.blue,
    },
    {
      'id': 'wait_return',
      'title': 'Wait & Return Later',
      'subtitle': 'Wait for customer and attempt delivery later',
      'icon': Icons.schedule,
      'color': Colors.orange,
    },
    {
      'id': 'leave_neighbor',
      'title': 'Leave with Neighbor',
      'subtitle': 'Deliver to a trusted neighbor',
      'icon': Icons.people,
      'color': Colors.green,
    },
    {
      'id': 'return_warehouse',
      'title': 'Return to Warehouse',
      'subtitle': 'Return order to warehouse for redelivery',
      'icon': Icons.warehouse,
      'color': Colors.red,
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeController();

    // Track screen view
    trackScreenView('customer_unavailable_screen', properties: {
      'order_id': widget.orderId,
      'customer_name': widget.customerName,
      'customer_phone': widget.customerPhone,
    });
  }

  void _initializeController() {
    _orderController = ControllerUtils.getOrderManagementController();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: CommonAppBar(
        titleKey: 'customerUnavailable',
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeaderSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Customer contact section
            _buildCustomerContactSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Action options
            _buildActionOptionsSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Notes section
            _buildNotesSection(),

            const Spacer(),

            // Action button
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title
          Row(
            children: [
              Container(
                width: ResponsiveUtils.scale(context, 50),
                height: ResponsiveUtils.scale(context, 50),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person_off,
                  color: Colors.orange,
                  size: ResponsiveUtils.scale(context, 24),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingM(context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Customer Unavailable',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 18),
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'Order ID: ${widget.orderId}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // Customer info
          Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Text(
                      widget.customerName,
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Expanded(
                      child: Text(
                        widget.customerAddress,
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 12),
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerContactSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Customer',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // Call button
          SizedBox(
            width: double.infinity,
            height: ResponsiveUtils.scale(context, 48),
            child: ElevatedButton.icon(
              onPressed: _callCustomer,
              icon: Icon(Icons.phone, color: Colors.white),
              label: Text(
                _hasCalledCustomer ? 'Call Again (${_callAttempts} attempts)' : 'Call Customer',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 14),
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                ),
              ),
            ),
          ),

          SizedBox(height: ResponsiveUtils.spacingS(context)),

          // SMS button
          SizedBox(
            width: double.infinity,
            height: ResponsiveUtils.scale(context, 48),
            child: OutlinedButton.icon(
              onPressed: _sendSMS,
              icon: Icon(Icons.message, color: Colors.blue),
              label: Text(
                'Send SMS',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 14),
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.blue),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionOptionsSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What would you like to do?',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          ...(_actionOptions.map((option) => _buildActionOption(option)).toList()),
        ],
      ),
    );
  }

  Widget _buildActionOption(Map<String, dynamic> option) {
    final isSelected = _selectedAction == option['id'];

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedAction = option['id'];
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: ResponsiveUtils.spacingS(context)),
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        decoration: BoxDecoration(
          color: isSelected ? option['color'].withValues(alpha: 0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
          border: Border.all(
            color: isSelected ? option['color'] : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: ResponsiveUtils.scale(context, 40),
              height: ResponsiveUtils.scale(context, 40),
              decoration: BoxDecoration(
                color: option['color'].withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                option['icon'],
                color: option['color'],
                size: ResponsiveUtils.scale(context, 20),
              ),
            ),
            SizedBox(width: ResponsiveUtils.spacingM(context)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option['title'],
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 14),
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: ResponsiveUtils.spacingXS(context)),
                  Text(
                    option['subtitle'],
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 12),
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: option['color'],
                size: ResponsiveUtils.scale(context, 20),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Notes',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          TextField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Add any additional notes about the delivery attempt...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                borderSide: BorderSide(color: AppColors.green, width: 2),
              ),
              contentPadding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    final canProceed = _selectedAction.isNotEmpty;

    return SafeArea(
      child: SizedBox(
        width: double.infinity,
        height: ResponsiveUtils.scale(context, 48),
        child: ElevatedButton(
          onPressed: (canProceed && !_isProcessing) ? _handleSelectedAction : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.green,
            disabledBackgroundColor: Colors.grey.shade300,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
            ),
          ),
          child: _isProcessing
              ? SizedBox(
                  width: ResponsiveUtils.scale(context, 20),
                  height: ResponsiveUtils.scale(context, 20),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  _getActionButtonText(),
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }

  String _getActionButtonText() {
    switch (_selectedAction) {
      case 'call_again':
        return 'Call Customer Again';
      case 'wait_return':
        return 'Schedule Return Later';
      case 'leave_neighbor':
        return 'Deliver to Neighbor';
      case 'return_warehouse':
        return 'Return to Warehouse';
      default:
        return 'Select an Action';
    }
  }

  void _callCustomer() {
    setState(() {
      _hasCalledCustomer = true;
      _callAttempts++;
    });

    // Track call attempt
    trackUserAction('call_customer', properties: {
      'order_id': widget.orderId,
      'customer_phone': widget.customerPhone,
      'call_attempt': _callAttempts,
    });

    // Simulate call functionality
    Get.snackbar(
      'Calling Customer',
      'Calling ${widget.customerPhone}...',
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  void _sendSMS() {
    // Track SMS send
    trackUserAction('send_sms', properties: {
      'order_id': widget.orderId,
      'customer_phone': widget.customerPhone,
    });

    // Simulate SMS functionality
    Get.snackbar(
      'SMS Sent',
      'Delivery notification sent to customer.',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  Future<void> _handleSelectedAction() async {
    setState(() => _isProcessing = true);

    try {
      // Track selected action
      trackDeliveryEvent('customer_unavailable_action', deliveryData: {
        'order_id': widget.orderId,
        'action': _selectedAction,
        'call_attempts': _callAttempts,
        'notes': _notesController.text,
      });

      switch (_selectedAction) {
        case 'call_again':
          _callCustomer();
          setState(() => _isProcessing = false);
          break;

        case 'wait_return':
          await _scheduleRedelivery();
          break;

        case 'leave_neighbor':
          await _deliverToNeighbor();
          break;

        case 'return_warehouse':
          await _returnToWarehouse();
          break;
      }
    } catch (e) {
      setState(() => _isProcessing = false);
      trackError('customer_unavailable_action_error', e.toString());

      Get.snackbar(
        'Error',
        'Failed to process action. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _scheduleRedelivery() async {
    // Update order status and schedule redelivery
    await Future.delayed(const Duration(seconds: 2));

    Get.snackbar(
      'Redelivery Scheduled',
      'Order will be attempted for delivery later.',
      backgroundColor: Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );

    // Navigate back to dashboard
    Future.delayed(const Duration(seconds: 2), () {
      Get.offAllNamed(AppRoutes.dashboard);
    });
  }

  Future<void> _deliverToNeighbor() async {
    // Handle neighbor delivery
    await Future.delayed(const Duration(seconds: 2));

    Get.snackbar(
      'Delivered to Neighbor',
      'Order has been delivered to a trusted neighbor.',
      backgroundColor: AppColors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );

    // Navigate to order summary
    Future.delayed(const Duration(seconds: 2), () {
      final completedOrders = _orderController?.completedOrders ?? [];
      Get.offNamed(AppRoutes.orderSummary, arguments: {
        'completedOrders': completedOrders,
        'totalEarnings': _orderController?.totalEarningsToday ?? 0.0,
        'totalDeliveries': _orderController?.deliveredOrders ?? 0,
      });
    });
  }

  Future<void> _returnToWarehouse() async {
    // Handle return to warehouse
    await _orderController?.cancelOrder(widget.orderId, 'Customer unavailable - returned to warehouse');

    Get.snackbar(
      'Returned to Warehouse',
      'Order has been returned to warehouse for redelivery.',
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );

    // Navigate back to dashboard
    Future.delayed(const Duration(seconds: 2), () {
      Get.offAllNamed(AppRoutes.dashboard);
    });
  }
}
