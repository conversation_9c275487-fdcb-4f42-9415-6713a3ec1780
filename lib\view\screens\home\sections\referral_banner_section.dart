import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_widgets.dart';

class ReferralBannerSection extends StatelessWidget {
  const ReferralBannerSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: ResponsiveUtils.spacingM(context)),
      width: ResponsiveUtils.width(context, 89.5), // 358px equivalent
      height: ResponsiveUtils.height(context, 9), // 72px equivalent
      padding: EdgeInsets.only(
        top: ResponsiveUtils.height(context, 1.5), // 12px
        right: ResponsiveUtils.spacingM(context), // 16px
        bottom: ResponsiveUtils.height(context, 1.5), // 12px
        left: ResponsiveUtils.spacingM(context), // 16px
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-0.5, -1.0), // Approximates 64.69 degrees
          end: Alignment(1.0, 1.0),
          colors: [
            Color(0xFF0A412D), // Dark green
            Color(0xFF1EE49B), // Light green
          ],
          stops: [0.1098, 1.3236], // 10.98% and 132.36%
        ),
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)), // 8px
        border: Border.all(
          color: const Color(0xFFEAECF0), // Border color
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Left side - Icon and text
          Expanded(
            child: Row(
              children: [
                // Referral gift box icon
                Container(
                  width: ResponsiveUtils.width(context, 12), // Icon container
                  height: ResponsiveUtils.width(context, 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                  ),
                  child: CachedAssetImage.icon(
                    assetPath: 'assets/images/gift_box_icon.png', // Your gift box icon
                    size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                    fit: BoxFit.contain,
                  ),
                ),

                SizedBox(width: ResponsiveUtils.spacingS(context)), // 10px gap

                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '₹5,000',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 18),
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFFFFCC00), // Yellow color
                        ),
                      ),
                      Text(
                        'Refer now, and earn more!',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 12),
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Right side - View button
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.spacingM(context),
              vertical: ResponsiveUtils.spacingXS(context),
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.large)),
            ),
            child: Text(
              'View',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 14),
                fontWeight: FontWeight.w500,
                color: const Color(0xFF0A412D), // Dark green text
              ),
            ),
          ),
        ],
      ),
    );
  }
}
