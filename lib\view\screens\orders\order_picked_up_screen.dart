import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class OrderPickedUpScreen extends StatefulWidget {
  const OrderPickedUpScreen({super.key});

  @override
  State<OrderPickedUpScreen> createState() => _OrderPickedUpScreenState();
}

class _OrderPickedUpScreenState extends State<OrderPickedUpScreen> {
  late PickupSession _pickupSession;
  late List<DeliveryOrderItem> _deliveryOrders;
  bool _isConfirming = false;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
    _generateDeliveryOrders();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _pickupSession = args?['pickupSession'] ?? _createDefaultSession();
  }

  PickupSession _createDefaultSession() {
    return PickupSession(
      sessionId: 'default_session',
      orders: const [],
      startTime: DateTime.now(),
    );
  }

  void _generateDeliveryOrders() {
    // Convert pickup orders to delivery orders with auto-priority
    final scannedOrders = _pickupSession.orders.where((order) => order.isCompletelyScanned).toList();

    _deliveryOrders = scannedOrders.asMap().entries.map((entry) {
      final index = entry.key;
      final order = entry.value;

      return DeliveryOrderItem(
        orderId: order.orderId,
        customerName: 'Customer ${index + 1}',
        address: _generateAddress(index),
        distance: _generateDistance(index),
        priority: index + 1,
        estimatedTime: _generateEstimatedTime(index),
        paymentMode: index % 2 == 0 ? 'COD' : 'Online',
        bagCount: order.saddleBags + order.silverBags + order.milkPouches,
        isSelected: false,
      );
    }).toList();

    // Sort by priority (nearest to farthest)
    _deliveryOrders.sort((a, b) => a.priority.compareTo(b.priority));
  }

  String _generateAddress(int index) {
    final addresses = [
      'Sector 15, Vashi, Navi Mumbai',
      'Sector 17, Vashi, Navi Mumbai',
      'Sector 19, Vashi, Navi Mumbai',
      'Sector 21, Vashi, Navi Mumbai',
      'Sector 23, Vashi, Navi Mumbai',
    ];
    return addresses[index % addresses.length];
  }

  double _generateDistance(int index) {
    return 2.5 + (index * 1.2); // Increasing distance
  }

  String _generateEstimatedTime(int index) {
    final baseTime = 15 + (index * 8); // Increasing time
    return '${baseTime} min';
  }

  String _getCurrentSlotTiming() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 7 && hour < 15) {
      return 'Morning Slot (7 AM - 3 PM)';
    } else if (hour >= 15 && hour < 22) {
      return 'Evening Slot (3 PM - 10 PM)';
    } else {
      return 'Night Slot (10 PM - 7 AM)';
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _deliveryOrders.removeAt(oldIndex);
      _deliveryOrders.insert(newIndex, item);

      // Update priorities
      for (int i = 0; i < _deliveryOrders.length; i++) {
        _deliveryOrders[i] = _deliveryOrders[i].copyWith(priority: i + 1);
      }
    });
  }

  void _toggleOrderSelection(int index) {
    setState(() {
      _deliveryOrders[index] = _deliveryOrders[index].copyWith(
        isSelected: !_deliveryOrders[index].isSelected,
      );
    });
  }

  void _confirmOrderSequence() {
    setState(() {
      _isConfirming = true;
    });

    // Simulate confirmation delay
    Future.delayed(const Duration(seconds: 2), () {
      // Navigate to first order delivery
      Get.offNamed(AppRoutes.firstOrderDelivery, arguments: {
        'deliveryOrders': _deliveryOrders,
        'currentOrderIndex': 0,
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Order Picked Up',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        actions: [
          // Slot timing
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppColors.green),
            ),
            child: Text(
              _getCurrentSlotTiming(),
              style: TextStyle(
                color: AppColors.green,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Delivery Sequence (${_deliveryOrders.length} orders)',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Drag to reorder or tap to select sequence',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),

          // Orders list with drag & drop
          Expanded(
            child: ReorderableListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _deliveryOrders.length,
              onReorder: _onReorder,
              itemBuilder: (context, index) {
                final order = _deliveryOrders[index];
                return _buildOrderCard(order, index);
              },
            ),
          ),

          // Confirm button with slide gesture
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: _buildConfirmButton(),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(DeliveryOrderItem order, int index) {
    return Container(
      key: ValueKey(order.orderId),
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: order.isSelected ? AppColors.green.withValues(alpha: 0.1) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: order.isSelected ? AppColors.green : Colors.grey.shade300,
          width: order.isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _toggleOrderSelection(index),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Priority number
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.green,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    order.priority.toString(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Order details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order ID and customer name
                    Row(
                      children: [
                        Text(
                          '#${order.orderId}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          order.customerName,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Address
                    Text(
                      order.address,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // Distance, time, payment
                    Row(
                      children: [
                        _buildInfoChip(
                          icon: Icons.location_on,
                          text: '${order.distance.toStringAsFixed(1)} km',
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          icon: Icons.access_time,
                          text: order.estimatedTime,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          icon: Icons.payment,
                          text: order.paymentMode,
                          color: order.paymentMode == 'COD' ? Colors.green : Colors.purple,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Drag handle and bag count
              Column(
                children: [
                  Icon(
                    Icons.drag_handle,
                    color: Colors.grey.shade400,
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${order.bagCount} bags',
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isConfirming ? null : _confirmOrderSequence,
        style: ElevatedButton.styleFrom(
          backgroundColor: _isConfirming ? Colors.grey : AppColors.green,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
        ),
        child: _isConfirming
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    'Confirming...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.swipe_right,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Slide to Confirm Order',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
