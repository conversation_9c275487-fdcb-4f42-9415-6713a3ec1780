import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

class RegistrationProgressIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepTitles;
  final bool showStepNumbers;
  final bool showStepTitles;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? completedColor;

  const RegistrationProgressIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.stepTitles = const [],
    this.showStepNumbers = true,
    this.showStepTitles = true,
    this.activeColor,
    this.inactiveColor,
    this.completedColor,
  });

  @override
  Widget build(BuildContext context) {
    final activeCol = activeColor ?? AppColors.green;
    final inactiveCol = inactiveColor ?? Colors.grey.shade300;
    final completedCol = completedColor ?? AppColors.green;

    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      child: Column(
        children: [
          // Progress bar
          _buildProgressBar(context, activeCol, inactiveCol),
          
          if (showStepTitles && stepTitles.isNotEmpty) ...[
            SizedBox(height: ResponsiveUtils.spacingS(context)),
            _buildStepTitles(context, activeCol, inactiveCol, completedCol),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context, Color activeCol, Color inactiveCol) {
    return Row(
      children: List.generate(totalSteps * 2 - 1, (index) {
        if (index.isEven) {
          // Step circle
          final stepIndex = index ~/ 2;
          return _buildStepCircle(context, stepIndex, activeCol, inactiveCol);
        } else {
          // Connector line
          final stepIndex = index ~/ 2;
          return _buildConnectorLine(context, stepIndex, activeCol, inactiveCol);
        }
      }),
    );
  }

  Widget _buildStepCircle(BuildContext context, int stepIndex, Color activeCol, Color inactiveCol) {
    final isCompleted = stepIndex < currentStep;
    final isActive = stepIndex == currentStep;
    final isInactive = stepIndex > currentStep;

    Color circleColor;
    Color textColor;
    Widget? icon;

    if (isCompleted) {
      circleColor = activeCol;
      textColor = Colors.white;
      icon = Icon(
        Icons.check,
        size: ResponsiveUtils.scale(context, 16),
        color: Colors.white,
      );
    } else if (isActive) {
      circleColor = activeCol;
      textColor = Colors.white;
    } else {
      circleColor = inactiveCol;
      textColor = Colors.grey.shade600;
    }

    return Container(
      width: ResponsiveUtils.scale(context, 32),
      height: ResponsiveUtils.scale(context, 32),
      decoration: BoxDecoration(
        color: circleColor,
        shape: BoxShape.circle,
        border: isActive ? Border.all(color: activeCol, width: 2) : null,
      ),
      child: Center(
        child: icon ?? (showStepNumbers 
          ? Text(
              '${stepIndex + 1}',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 12),
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            )
          : const SizedBox.shrink()),
      ),
    );
  }

  Widget _buildConnectorLine(BuildContext context, int stepIndex, Color activeCol, Color inactiveCol) {
    final isCompleted = stepIndex < currentStep;
    final lineColor = isCompleted ? activeCol : inactiveCol;

    return Expanded(
      child: Container(
        height: 2,
        color: lineColor,
      ),
    );
  }

  Widget _buildStepTitles(BuildContext context, Color activeCol, Color inactiveCol, Color completedCol) {
    return Row(
      children: List.generate(totalSteps, (index) {
        final isCompleted = index < currentStep;
        final isActive = index == currentStep;
        
        Color textColor;
        FontWeight fontWeight;

        if (isCompleted) {
          textColor = completedCol;
          fontWeight = FontWeight.w500;
        } else if (isActive) {
          textColor = activeCol;
          fontWeight = FontWeight.w600;
        } else {
          textColor = Colors.grey.shade600;
          fontWeight = FontWeight.normal;
        }

        final title = index < stepTitles.length ? stepTitles[index] : 'Step ${index + 1}';

        return Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 10),
              fontWeight: fontWeight,
              color: textColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        );
      }),
    );
  }
}

class RegistrationStepCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget child;
  final VoidCallback? onBack;
  final bool showBackButton;
  final int? currentStep;
  final int? totalSteps;

  const RegistrationStepCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.child,
    this.onBack,
    this.showBackButton = true,
    this.currentStep,
    this.totalSteps,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with back button and title
        if (showBackButton || currentStep != null)
          _buildHeader(context),
        
        // Main content card
        Expanded(
          child: Container(
            width: double.infinity,
            margin: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step title and subtitle
                _buildStepHeader(context),
                
                SizedBox(height: ResponsiveUtils.spacingL(context)),
                
                // Step content
                Expanded(child: child),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      child: Row(
        children: [
          // Back button
          if (showBackButton && onBack != null)
            IconButton(
              onPressed: onBack,
              icon: Icon(
                Icons.arrow_back_ios,
                size: ResponsiveUtils.scale(context, 20),
                color: Colors.black87,
              ),
            ),
          
          // Step progress
          if (currentStep != null && totalSteps != null) ...[
            if (showBackButton && onBack != null) const Spacer(),
            Text(
              'Step ${currentStep! + 1} of $totalSteps',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 14),
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          
          if (!showBackButton || onBack == null) const Spacer(),
        ],
      ),
    );
  }

  Widget _buildStepHeader(BuildContext context) {
    return Row(
      children: [
        // Icon
        if (icon != null) ...[
          Container(
            width: ResponsiveUtils.scale(context, 48),
            height: ResponsiveUtils.scale(context, 48),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Icon(
              icon,
              size: ResponsiveUtils.scale(context, 24),
              color: AppColors.green,
            ),
          ),
          SizedBox(width: ResponsiveUtils.spacingM(context)),
        ],
        
        // Title and subtitle
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(context, 20),
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (subtitle != null) ...[
                SizedBox(height: ResponsiveUtils.spacingXS(context)),
                Text(
                  subtitle!,
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 14),
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

class RegistrationStepButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isSecondary;

  const RegistrationStepButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isSecondary = false,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = backgroundColor ?? (isSecondary ? Colors.transparent : AppColors.green);
    final txtColor = textColor ?? (isSecondary ? AppColors.green : Colors.white);
    final enabled = isEnabled && !isLoading && onPressed != null;

    if (isSecondary) {
      return SizedBox(
        width: double.infinity,
        height: ResponsiveUtils.scale(context, 48),
        child: OutlinedButton(
          onPressed: enabled ? onPressed : null,
          style: OutlinedButton.styleFrom(
            side: BorderSide(color: enabled ? AppColors.green : Colors.grey.shade300),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
            ),
          ),
          child: _buildButtonContent(context, txtColor, enabled),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      height: ResponsiveUtils.scale(context, 48),
      child: ElevatedButton(
        onPressed: enabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: enabled ? bgColor : Colors.grey.shade300,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
          ),
        ),
        child: _buildButtonContent(context, enabled ? txtColor : Colors.grey.shade600, enabled),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context, Color color, bool enabled) {
    if (isLoading) {
      return SizedBox(
        width: ResponsiveUtils.scale(context, 20),
        height: ResponsiveUtils.scale(context, 20),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: ResponsiveUtils.scale(context, 18), color: color),
          SizedBox(width: ResponsiveUtils.spacingS(context)),
          Text(
            text,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: ResponsiveUtils.fontSize(context, 16),
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }
}
