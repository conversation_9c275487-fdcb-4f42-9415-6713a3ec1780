import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import '../../../mixins/analytics_mixin.dart';
import '../../../services/all_services.dart';
import '../../widgets/common/common_app_bar.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class QRScannerScreen extends StatefulWidget {
  final String scanType;
  final String orderId;

  const QRScannerScreen({
    super.key,
    this.scanType = 'Saddle bag',
    this.orderId = '',
  });

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> with StatefulAnalyticsMixin {
  late final GlobalKey qrKey;
  QRViewController? controller;
  bool flashOn = false;
  bool _isScanning = true;

  @override
  void initState() {
    super.initState();
    qrKey = GlobalKey(debugLabel: 'QR_${widget.scanType}_${DateTime.now().millisecondsSinceEpoch}');
  }

  @override
  String get screenName => 'QRScannerScreen';

  @override
  void dispose() {
    _isScanning = false;
    super.dispose();
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      // Handle scanned QR code
      if (scanData.code != null && _isScanning) {
        _isScanning = false; // Prevent multiple scans
        controller.pauseCamera();
        _handleScannedCode(scanData.code!);
      }
    });
  }

  void _handleScannedCode(String code) {
    if (!_isScanning) {
      // Track successful QR scan
      trackQRScanEvent(widget.scanType, true, orderId: widget.orderId);

      // Show success dialog or navigate to next screen
      Get.toNamed(AppRoutes.orderPickedUp);
    }
  }

  void _toggleFlash() {
    setState(() {
      flashOn = !flashOn;
    });
    controller?.toggleFlash();

    // Track flash toggle
    trackUserAction('flash_toggled', properties: {
      'flash_on': flashOn,
      'scan_type': widget.scanType,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF808080), // Gray background like in design
      appBar: CommonAppBar(
        titleKey: 'scanQRCode',
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Background with saddle bag image
          Container(
            width: double.infinity,
            height: double.infinity,
            color: const Color(0xFF808080), // Gray background
            child: Center(
              child: SizedBox(
                width: ResponsiveUtils.scale(context, 300),
                height: ResponsiveUtils.scale(context, 400),
                child: Stack(
                  children: [
                    // Saddle bag image
                    Positioned.fill(
                      child: Image.asset(
                        'assets/images/saddle_bag.png',
                        fit: BoxFit.contain,
                      ),
                    ),

                    // QR Code overlay positioned on the bag
                    Positioned(
                      bottom: ResponsiveUtils.scale(context, 80),
                      left: ResponsiveUtils.scale(context, 50),
                      right: ResponsiveUtils.scale(context, 50),
                      child: Container(
                        width: ResponsiveUtils.scale(context, 200),
                        height: ResponsiveUtils.scale(context, 200),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4CAF50), // Green background for QR area
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.white,
                            width: 4,
                          ),
                        ),
                        child: Stack(
                          children: [
                            // QR Scanner Camera View
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: QRView(
                                key: qrKey,
                                onQRViewCreated: _onQRViewCreated,
                                overlay: QrScannerOverlayShape(
                                  borderColor: Colors.transparent, // Hide default overlay
                                  borderRadius: 0,
                                  borderLength: 0,
                                  borderWidth: 0,
                                  cutOutSize: ResponsiveUtils.scale(context, 180),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom buttons container with proper alignment
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.spacingL(context),
                vertical: ResponsiveUtils.spacingXL(context),
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Can't Scan Button
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: ResponsiveUtils.spacingS(context)),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Track manual skip of QR scan
                          trackQRScanEvent(widget.scanType, false, orderId: widget.orderId);
                          trackUserAction('cant_scan_button_pressed', properties: {
                            'scan_type': widget.scanType,
                            'order_id': widget.orderId,
                          });
                          Get.toNamed(AppRoutes.orderPickedUp);
                        },
                        icon: Icon(
                          Icons.qr_code_2,
                          size: ResponsiveUtils.iconSize(context, IconSizeType.small),
                          color: AppColors.textPrimary,
                        ),
                        label: Text(
                          AppStrings.get('cantScan'),
                          style: AppTextTheme.responsive(context, AppTextTheme.buttonMedium).copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.textPrimary,
                          padding: EdgeInsets.symmetric(
                            vertical: ResponsiveUtils.spacingS(context),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(ResponsiveUtils.scale(context, 25)),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Flash Toggle Button
                  Container(
                    width: ResponsiveUtils.scale(context, 56),
                    height: ResponsiveUtils.scale(context, 56),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _toggleFlash,
                      icon: Icon(
                        flashOn ? Icons.flash_on : Icons.flash_off,
                        color: AppColors.textPrimary,
                        size: ResponsiveUtils.iconSize(context, IconSizeType.medium),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
