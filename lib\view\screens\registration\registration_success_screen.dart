import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/mixins/analytics_mixin.dart';
import 'package:lottie/lottie.dart';

class RegistrationSuccessScreen extends StatefulWidget {
  final String? riderName;
  final String? mobileNumber;
  final int? approvalStatus; // 0: pending, 1: approved, 2: rejected

  const RegistrationSuccessScreen({
    super.key,
    this.riderName,
    this.mobileNumber,
    this.approvalStatus,
  });

  @override
  State<RegistrationSuccessScreen> createState() => _RegistrationSuccessScreenState();
}

class _RegistrationSuccessScreenState extends State<RegistrationSuccessScreen> with AnalyticsMixin {
  bool _showConfetti = true;

  @override
  void initState() {
    super.initState();
    
    // Track registration completion
    trackUserAction('registration_completed', properties: {
      'rider_name': widget.riderName ?? 'Unknown',
      'mobile_number': widget.mobileNumber ?? 'Unknown',
      'approval_status': widget.approvalStatus ?? 0,
    });

    // Hide confetti after animation
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() => _showConfetti = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            _buildMainContent(),
            
            // Confetti animation overlay
            if (_showConfetti) _buildConfettiOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      child: Column(
        children: [
          const Spacer(),
          
          // Success animation/icon
          _buildSuccessIcon(),
          
          SizedBox(height: ResponsiveUtils.spacingXL(context)),
          
          // Success message
          _buildSuccessMessage(),
          
          SizedBox(height: ResponsiveUtils.spacingL(context)),
          
          // Status card
          _buildStatusCard(),
          
          const Spacer(),
          
          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: ResponsiveUtils.scale(context, 120),
      height: ResponsiveUtils.scale(context, 120),
      decoration: BoxDecoration(
        color: AppColors.green.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Animated checkmark
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Icon(
                  Icons.check_circle,
                  size: ResponsiveUtils.scale(context, 80),
                  color: AppColors.green,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      children: [
        Text(
          'Registration Successful!',
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 24),
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: ResponsiveUtils.spacingS(context)),
        
        if (widget.riderName != null)
          Text(
            'Welcome, ${widget.riderName}!',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 18),
              fontWeight: FontWeight.w500,
              color: AppColors.green,
            ),
            textAlign: TextAlign.center,
          ),
        
        SizedBox(height: ResponsiveUtils.spacingM(context)),
        
        Text(
          _getStatusMessage(),
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 14),
            color: Colors.grey.shade600,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        border: Border.all(
          color: _getStatusColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Status icon and title
          Row(
            children: [
              Container(
                width: ResponsiveUtils.scale(context, 40),
                height: ResponsiveUtils.scale(context, 40),
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getStatusIcon(),
                  color: Colors.white,
                  size: ResponsiveUtils.scale(context, 20),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingM(context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusTitle(),
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 16),
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.spacingXS(context)),
                    Text(
                      _getStatusSubtitle(),
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 12),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          
          // Additional info
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.mobileNumber != null) ...[
                  _buildInfoRow('Mobile Number', widget.mobileNumber!),
                  SizedBox(height: ResponsiveUtils.spacingS(context)),
                ],
                _buildInfoRow('Registration Date', _getCurrentDate()),
                SizedBox(height: ResponsiveUtils.spacingS(context)),
                _buildInfoRow('Status', _getStatusTitle()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 12),
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 12),
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Primary action button
        SizedBox(
          width: double.infinity,
          height: ResponsiveUtils.scale(context, 48),
          child: ElevatedButton(
            onPressed: _handlePrimaryAction,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
            ),
            child: Text(
              _getPrimaryActionText(),
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 16),
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
        
        SizedBox(height: ResponsiveUtils.spacingM(context)),
        
        // Secondary action button
        SizedBox(
          width: double.infinity,
          height: ResponsiveUtils.scale(context, 48),
          child: OutlinedButton(
            onPressed: _handleSecondaryAction,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey.shade400),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
            ),
            child: Text(
              'Contact Support',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 16),
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfettiOverlay() {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: Center(
            child: Icon(
              Icons.celebration,
              size: ResponsiveUtils.scale(context, 200),
              color: AppColors.green.withValues(alpha: 0.3),
            ),
          ),
        ),
      ),
    );
  }

  String _getStatusMessage() {
    switch (widget.approvalStatus) {
      case 1:
        return 'Your registration has been approved! You can now start accepting delivery orders.';
      case 2:
        return 'Your registration was not approved. Please contact support for more information.';
      default:
        return 'Your registration is complete and under review. We will notify you once it\'s approved.';
    }
  }

  Color _getStatusColor() {
    switch (widget.approvalStatus) {
      case 1:
        return AppColors.green;
      case 2:
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.approvalStatus) {
      case 1:
        return Icons.verified;
      case 2:
        return Icons.cancel;
      default:
        return Icons.pending;
    }
  }

  String _getStatusTitle() {
    switch (widget.approvalStatus) {
      case 1:
        return 'Approved';
      case 2:
        return 'Not Approved';
      default:
        return 'Under Review';
    }
  }

  String _getStatusSubtitle() {
    switch (widget.approvalStatus) {
      case 1:
        return 'You can start delivering orders';
      case 2:
        return 'Please contact support';
      default:
        return 'We will notify you soon';
    }
  }

  String _getPrimaryActionText() {
    switch (widget.approvalStatus) {
      case 1:
        return 'Start Delivering';
      case 2:
        return 'Try Again';
      default:
        return 'Continue to App';
    }
  }

  String _getCurrentDate() {
    final now = DateTime.now();
    return '${now.day}/${now.month}/${now.year}';
  }

  void _handlePrimaryAction() {
    // Track primary action
    trackUserAction('registration_success_primary_action', properties: {
      'approval_status': widget.approvalStatus ?? 0,
      'action': _getPrimaryActionText(),
    });

    switch (widget.approvalStatus) {
      case 1:
        // Approved - go to dashboard
        Get.offAllNamed(AppRoutes.dashboard);
        break;
      case 2:
        // Not approved - restart registration
        Get.offAllNamed(AppRoutes.createProfile);
        break;
      default:
        // Pending - go to welcome screen
        Get.offAllNamed(AppRoutes.deliveryPartnerWelcome);
        break;
    }
  }

  void _handleSecondaryAction() {
    // Track secondary action
    trackUserAction('registration_success_contact_support', properties: {
      'approval_status': widget.approvalStatus ?? 0,
    });

    // Show contact support dialog or navigate to support screen
    _showContactSupportDialog();
  }

  void _showContactSupportDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Contact Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help with your registration?'),
            SizedBox(height: 16),
            Text('📞 Call: +91 1234567890'),
            SizedBox(height: 8),
            Text('📧 Email: <EMAIL>'),
            SizedBox(height: 8),
            Text('💬 WhatsApp: +91 1234567890'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
