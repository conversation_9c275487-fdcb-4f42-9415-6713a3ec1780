import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Rain animation widget for heavy rain effect
class RainAnimation extends StatefulWidget {
  final double intensity; // 0.0 to 1.0
  final Color rainColor;
  final double width;
  final double height;

  const RainAnimation({
    Key? key,
    this.intensity = 1.0,
    this.rainColor = Colors.white,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  State<RainAnimation> createState() => _RainAnimationState();
}

class _RainAnimationState extends State<RainAnimation> with TickerProviderStateMixin {
  late AnimationController _controller;
  List<RainDrop> rainDrops = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 100),
      vsync: this,
    )..repeat();

    _generateRainDrops();
    _controller.addListener(() {
      _updateRainDrops();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _generateRainDrops() {
    final random = math.Random();
    final dropCount = (widget.intensity * 100).round();

    rainDrops.clear();
    for (int i = 0; i < dropCount; i++) {
      rainDrops.add(RainDrop(
        x: random.nextDouble() * widget.width,
        y: random.nextDouble() * widget.height,
        speed: 2 + random.nextDouble() * 3,
        length: 10 + random.nextDouble() * 20,
        opacity: 0.3 + random.nextDouble() * 0.7,
      ));
    }
  }

  void _updateRainDrops() {
    for (var drop in rainDrops) {
      drop.y += drop.speed;
      if (drop.y > widget.height) {
        drop.y = -drop.length;
        drop.x = math.Random().nextDouble() * widget.width;
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(widget.width, widget.height),
      painter: RainPainter(
        rainDrops: rainDrops,
        rainColor: widget.rainColor,
      ),
    );
  }
}

/// Rain drop model
class RainDrop {
  double x;
  double y;
  final double speed;
  final double length;
  final double opacity;

  RainDrop({
    required this.x,
    required this.y,
    required this.speed,
    required this.length,
    required this.opacity,
  });
}

/// Custom painter for rain drops
class RainPainter extends CustomPainter {
  final List<RainDrop> rainDrops;
  final Color rainColor;

  RainPainter({
    required this.rainDrops,
    required this.rainColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (var drop in rainDrops) {
      final paint = Paint()
        ..color = rainColor.withOpacity(drop.opacity)
        ..strokeWidth = 1.5
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(
        Offset(drop.x, drop.y),
        Offset(drop.x, drop.y + drop.length),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Weather alert banner widget with exact design match
class WeatherAlertBanner extends StatelessWidget {
  final String message;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;

  const WeatherAlertBanner({
    Key? key,
    required this.message,
    this.icon = Icons.warning_amber_rounded,
    this.backgroundColor = const Color(0xFF4A5568), // Dark gray-blue
    this.textColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(0x14000000), // #00000014
            offset: Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              color: Colors.white,
              size: 14,
            ),
          ),
          SizedBox(width: 8),
          Text(
            "Online",
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 12),
          // Toggle switch
          Container(
            width: 50,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              children: [
                Positioned(
                  right: 2,
                  top: 2,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        "✓",
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 12),
          // +25 indicator
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.flash_on,
                  color: Colors.white,
                  size: 14,
                ),
                SizedBox(width: 4),
                Text(
                  "+25",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Lightning effect widget
class LightningEffect extends StatefulWidget {
  final bool isActive;
  final Color lightningColor;

  const LightningEffect({
    Key? key,
    this.isActive = false,
    this.lightningColor = Colors.white,
  }) : super(key: key);

  @override
  State<LightningEffect> createState() => _LightningEffectState();
}

class _LightningEffectState extends State<LightningEffect> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);

    if (widget.isActive) {
      _startLightning();
    }
  }

  @override
  void didUpdateWidget(LightningEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive && !oldWidget.isActive) {
      _startLightning();
    }
  }

  void _startLightning() {
    // Random lightning flashes
    Future.delayed(Duration(milliseconds: math.Random().nextInt(3000)), () {
      if (widget.isActive && mounted) {
        _controller.forward().then((_) {
          _controller.reverse().then((_) {
            _startLightning();
          });
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: widget.lightningColor.withOpacity(_animation.value * 0.3),
          ),
        );
      },
    );
  }
}
