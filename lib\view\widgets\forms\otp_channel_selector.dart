import 'package:flutter/material.dart';

/// Widget for selecting OTP delivery channel (SMS or WhatsApp)
class OtpChannelSelector extends StatefulWidget {
  final String selectedChannel;
  final Function(String) onChannelChanged;
  final bool isLoading;
  final bool showWhatsApp;

  const OtpChannelSelector({
    super.key,
    required this.selectedChannel,
    required this.onChannelChanged,
    this.isLoading = false,
    this.showWhatsApp = true,
  });

  @override
  State<OtpChannelSelector> createState() => _OtpChannelSelectorState();
}

class _OtpChannelSelectorState extends State<OtpChannelSelector> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Choose how to receive your OTP:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // SMS Option
          _buildChannelOption(
            channel: 'sms',
            title: 'SMS',
            subtitle: 'Receive OTP via text message',
            icon: Icons.sms,
            iconColor: Colors.blue,
          ),

          if (widget.showWhatsApp) ...[
            const SizedBox(height: 12),

            // WhatsApp Option
            _buildChannelOption(
              channel: 'whatsapp',
              title: 'WhatsApp',
              subtitle: 'Receive OTP via WhatsApp message',
              icon: Icons.chat,
              iconColor: Colors.green,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChannelOption({
    required String channel,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
  }) {
    final isSelected = widget.selectedChannel == channel;

    return GestureDetector(
      onTap: widget.isLoading ? null : () => widget.onChannelChanged(channel),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? iconColor.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? iconColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? iconColor : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            if (isSelected)
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: iconColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              )
            else
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Simple channel selector with radio buttons
class SimpleOtpChannelSelector extends StatelessWidget {
  final String selectedChannel;
  final Function(String) onChannelChanged;
  final bool showWhatsApp;

  const SimpleOtpChannelSelector({
    super.key,
    required this.selectedChannel,
    required this.onChannelChanged,
    this.showWhatsApp = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Receive OTP via:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // SMS Radio
            Expanded(
              child: RadioListTile<String>(
                value: 'sms',
                groupValue: selectedChannel,
                onChanged: (value) => onChannelChanged(value ?? 'sms'),
                title: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.sms, color: Colors.blue, size: 20),
                    SizedBox(width: 8),
                    Text('SMS'),
                  ],
                ),
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            ),

            if (showWhatsApp)
              Expanded(
                child: RadioListTile<String>(
                  value: 'whatsapp',
                  groupValue: selectedChannel,
                  onChanged: (value) => onChannelChanged(value ?? 'sms'),
                  title: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.chat, color: Colors.green, size: 20),
                      SizedBox(width: 8),
                      Text('WhatsApp'),
                    ],
                  ),
                  contentPadding: EdgeInsets.zero,
                  dense: true,
                ),
              ),
          ],
        ),
      ],
    );
  }
}

/// Compact toggle for SMS/WhatsApp
class CompactOtpChannelToggle extends StatelessWidget {
  final String selectedChannel;
  final Function(String) onChannelChanged;

  const CompactOtpChannelToggle({
    super.key,
    required this.selectedChannel,
    required this.onChannelChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton('sms', 'SMS', Icons.sms, Colors.blue),
          _buildToggleButton('whatsapp', 'WhatsApp', Icons.chat, Colors.green),
        ],
      ),
    );
  }

  Widget _buildToggleButton(
    String channel,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = selectedChannel == channel;

    return GestureDetector(
      onTap: () => onChannelChanged(channel),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : color,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : color,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
