import 'package:flutter/material.dart';

import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_widgets.dart';

class IncentiveBannerSection extends StatelessWidget {
  const IncentiveBannerSection({super.key});

  @override
  Widget build(BuildContext context) {
    // Using ResponsiveUtils for consistent sizing
    final containerMargin = ResponsiveUtils.spacingM(context); // 4% of screen width
    final containerRadius = ResponsiveUtils.borderRadius(context, BorderRadiusType.medium); // 4% of screen width
    final progressBarHeight = ResponsiveUtils.height(context, 1.5); // 1.5% of screen height
    final rupeeIconSize = ResponsiveUtils.iconSize(context, IconSizeType.large); // Responsive icon size
    final giftIconSize = ResponsiveUtils.iconSize(context, IconSizeType.extraLarge); // Responsive icon size
    final buttonPadding = ResponsiveUtils.spacingL(context); // 6% of screen width

    return Container(
      margin: EdgeInsets.symmetric(
        vertical: ResponsiveUtils.height(context, 1),
        horizontal: containerMargin,
      ),
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-1.0, 0.0), // left
          end: Alignment(1.0, 0.0), // right
          stops: [0.0055, 0.1965, 0.7081, 0.9909],
          colors: [
            Color.fromRGBO(236, 182, 52, 0.9), // 0.55%
            Color.fromRGBO(247, 239, 138, 0.9), // 19.65%
            Color.fromRGBO(210, 172, 71, 0.9), // 70.81%
            Color.fromRGBO(237, 201, 103, 0.9), // 99.09%
          ],
        ),
        borderRadius: BorderRadius.circular(containerRadius),
      ),
      child: Stack(
        children: [
          // Responsive diagonal stripes
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Opacity(
              opacity: 0.15,
              child: Container(
                width: ResponsiveUtils.width(context, 15), // 15% of screen width
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.white, Colors.transparent],
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                  ),
                ),
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Responsive progress bar with rupee coin
              Padding(
                padding: EdgeInsets.only(
                  top: ResponsiveUtils.height(context, 1.5),
                  left: containerMargin,
                  right: containerMargin,
                ),
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    Container(
                      height: progressBarHeight,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(progressBarHeight / 2),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(progressBarHeight / 2),
                        child: Container(
                          width: double.infinity,
                          height: progressBarHeight,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(progressBarHeight / 2),
                          ),
                        ),
                      ),
                    ),
                    // Responsive rupee coin as thumb
                    Positioned(
                      left: ((2 / 7) * (ResponsiveUtils.width(context, 100) - (containerMargin * 2) - rupeeIconSize)),
                      child: CachedAssetImage.icon(
                        assetPath: 'assets/images/rupee.png',
                        size: rupeeIconSize,
                      ),
                    ),
                    Positioned(
                      right: 0,
                      child: Text(
                        '2/7',
                        style: AppTextTheme.buttonSmall.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 14),
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: ResponsiveUtils.height(context, 1)),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: containerMargin),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Responsive gift icon
                    Container(
                      width: giftIconSize,
                      height: giftIconSize,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(containerRadius * 0.75),
                      ),
                      child: Center(
                        child: CachedAssetImage.icon(
                          assetPath: "assets/images/money-box.png",
                          size: giftIconSize * 0.7,
                        ),
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    // Responsive texts
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Earn More Incentive!',
                            style: AppTextTheme.cardTitle.copyWith(
                              fontSize: AppTextTheme.getResponsiveFontSize(context, 16),
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.height(context, 0.2)),
                          Text(
                            'By completing a daily simple task',
                            style: AppTextTheme.cardSubtitle.copyWith(
                              fontSize: AppTextTheme.getResponsiveFontSize(context, 13),
                              fontWeight: FontWeight.normal,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    // Responsive view button
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.extraLarge)),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: buttonPadding,
                          vertical: ResponsiveUtils.height(context, 1),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'View',
                        style: AppTextTheme.buttonSmall.copyWith(
                          fontSize: AppTextTheme.getResponsiveFontSize(context, 15),
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: ResponsiveUtils.height(context, 1.5)),
            ],
          ),
        ],
      ),
    );
  }
}
