import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_widgets.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class TodaysProgressSection extends StatelessWidget {
  const TodaysProgressSection({super.key});
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Responsive sizing
    final horizontalPadding = screenWidth * 0.04; // 4% of screen width
    final containerMargin = screenWidth * 0.04; // 4% of screen width
    final containerPadding = screenWidth * 0.04; // 4% of screen width
    final containerRadius = screenWidth * 0.04; // 4% of screen width
    final progressBoxHeight = screenHeight * 0.09; // 9% of screen height
    final iconContainerSize = screenWidth * 0.08; // 8% of screen width
    final iconSize = screenWidth * 0.05; // 5% of screen width

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header outside the container
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              Text(
                "Today's Progress",
                style: AppTextTheme.cardTitle,
              ),
              const Spacer(),
            ],
          ),
        ),

        SizedBox(height: screenHeight * 0.015),

        // Container content
        Container(
          width: screenWidth - (containerMargin * 2),
          margin: EdgeInsets.symmetric(horizontal: containerMargin),
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(containerRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: screenWidth * 0.02,
                offset: Offset(0, screenHeight * 0.002),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Responsive progress info boxes
              Row(
                children: [
                  _ProgressInfoBox(
                    label: 'Login hours',
                    value: '08:30 hrs',
                    iconPath: 'assets/images/working-time.png',
                    height: progressBoxHeight,
                    iconContainerSize: iconContainerSize,
                    iconSize: iconSize,
                  ),
                  SizedBox(width: screenWidth * 0.03),
                  _ProgressInfoBox(
                    label: 'Orders',
                    value: '04',
                    iconPath: 'assets/images/booking.png',
                    height: progressBoxHeight,
                    iconContainerSize: iconContainerSize,
                    iconSize: iconSize,
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.015),
              // Responsive "View all earnings" section
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  horizontal: containerPadding,
                  vertical: screenHeight * 0.015,
                ),
                decoration: BoxDecoration(
                  color: AppColors.greenLight,
                  borderRadius: BorderRadius.circular(containerRadius * 0.75),
                ),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(AppRoutes.allEarnings);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'View all earnings',
                        style: AppTextTheme.cardSubtitle.copyWith(
                          color: AppColors.green,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Icon(
                        Icons.arrow_forward,
                        color: AppColors.green,
                        size: screenWidth * 0.045,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _ProgressInfoBox extends StatelessWidget {
  final String label;
  final String value;
  final String iconPath;
  final double height;
  final double iconContainerSize;
  final double iconSize;

  const _ProgressInfoBox({
    required this.label,
    required this.value,
    required this.iconPath,
    required this.height,
    required this.iconContainerSize,
    required this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final cardRadius = screenWidth * 0.03; // 3% of screen width
    final iconRadius = screenWidth * 0.02; // 2% of screen width

    return Expanded(
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(cardRadius),
          border: Border.all(color: AppColors.borderLight, width: 1),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: screenWidth * 0.025),
            Container(
              width: iconContainerSize,
              height: iconContainerSize,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(iconRadius),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: screenWidth * 0.01,
                    offset: Offset(0, screenHeight * 0.002),
                  ),
                ],
              ),
              child: Center(
                child: CachedAssetImage.icon(
                  assetPath: iconPath,
                  size: iconSize,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            SizedBox(width: screenWidth * 0.025),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: AppTextTheme.cardCaption.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.002),
                  Text(
                    value,
                    style: label == 'Login hours'
                        ? AppTextTheme.textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold)
                        : AppTextTheme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
