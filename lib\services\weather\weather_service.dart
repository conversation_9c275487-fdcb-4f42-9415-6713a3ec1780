import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:kisankonnect_rider/services/all_services.dart';

/// Weather service to detect rain conditions
class WeatherService {
  static WeatherService? _instance;
  static WeatherService get instance => _instance ??= WeatherService._();

  late ApiHelper _apiHelper;
  WeatherCondition _currentCondition = WeatherCondition.clear;
  double _rainIntensity = 0.0;

  WeatherService._() {
    _apiHelper = ApiHelper.instance;
  }

  /// Get current weather condition
  WeatherCondition get currentCondition => _currentCondition;
  
  /// Get rain intensity (0.0 to 1.0)
  double get rainIntensity => _rainIntensity;
  
  /// Check if it's currently raining heavily
  bool get isHeavyRain => _currentCondition == WeatherCondition.heavyRain;
  
  /// Check if it's raining (any intensity)
  bool get isRaining => _currentCondition == WeatherCondition.lightRain || 
                       _currentCondition == WeatherCondition.moderateRain || 
                       _currentCondition == WeatherCondition.heavyRain;

  /// Fetch current weather data
  Future<WeatherData?> getCurrentWeather() async {
    try {
      // Get current location
      final position = await _getCurrentPosition();
      if (position == null) return null;

      // Option 1: Use OpenWeatherMap API (free tier)
      final response = await _apiHelper.get<dynamic>(
        'https://api.openweathermap.org/data/2.5/weather',
        queryParameters: {
          'lat': position.latitude,
          'lon': position.longitude,
          'appid': 'YOUR_OPENWEATHER_API_KEY', // Replace with actual API key
          'units': 'metric',
        },
      );

      if (response.isSuccess && response.data != null) {
        final weatherData = WeatherData.fromJson(response.data);
        _updateWeatherCondition(weatherData);
        return weatherData;
      }

      // Option 2: Fallback to mock data for testing
      return _getMockWeatherData();
    } catch (e) {
      debugPrint('❌ Weather fetch error: $e');
      // Return mock data for testing
      return _getMockWeatherData();
    }
  }

  /// Get current position
  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return null;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      debugPrint('❌ Location error: $e');
      return null;
    }
  }

  /// Update weather condition based on weather data
  void _updateWeatherCondition(WeatherData weatherData) {
    final weatherMain = weatherData.weather.toLowerCase();
    final rainVolume = weatherData.rainVolume;

    if (weatherMain.contains('rain')) {
      if (rainVolume >= 10.0) {
        _currentCondition = WeatherCondition.heavyRain;
        _rainIntensity = 1.0;
      } else if (rainVolume >= 2.5) {
        _currentCondition = WeatherCondition.moderateRain;
        _rainIntensity = 0.6;
      } else {
        _currentCondition = WeatherCondition.lightRain;
        _rainIntensity = 0.3;
      }
    } else if (weatherMain.contains('cloud')) {
      _currentCondition = WeatherCondition.cloudy;
      _rainIntensity = 0.0;
    } else {
      _currentCondition = WeatherCondition.clear;
      _rainIntensity = 0.0;
    }

    debugPrint('🌦️ Weather updated: $_currentCondition, Rain: ${_rainIntensity}');
  }

  /// Get mock weather data for testing
  WeatherData _getMockWeatherData() {
    // Simulate heavy rain for testing
    _currentCondition = WeatherCondition.heavyRain;
    _rainIntensity = 0.8;
    
    return WeatherData(
      temperature: 25.0,
      humidity: 85,
      weather: 'Heavy Rain',
      description: 'Heavy rain with thunderstorms',
      rainVolume: 15.0,
      windSpeed: 12.5,
    );
  }

  /// Start weather monitoring (call this in app initialization)
  void startWeatherMonitoring() {
    // Update weather every 10 minutes
    Stream.periodic(Duration(minutes: 10)).listen((_) {
      getCurrentWeather();
    });
    
    // Initial weather fetch
    getCurrentWeather();
  }
}

/// Weather condition enum
enum WeatherCondition {
  clear,
  cloudy,
  lightRain,
  moderateRain,
  heavyRain,
  thunderstorm,
}

/// Weather data model
class WeatherData {
  final double temperature;
  final int humidity;
  final String weather;
  final String description;
  final double rainVolume; // mm/hour
  final double windSpeed;

  WeatherData({
    required this.temperature,
    required this.humidity,
    required this.weather,
    required this.description,
    required this.rainVolume,
    required this.windSpeed,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      temperature: (json['main']?['temp'] ?? 0.0).toDouble(),
      humidity: json['main']?['humidity'] ?? 0,
      weather: json['weather']?[0]?['main'] ?? 'Clear',
      description: json['weather']?[0]?['description'] ?? 'Clear sky',
      rainVolume: (json['rain']?['1h'] ?? 0.0).toDouble(),
      windSpeed: (json['wind']?['speed'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'temperature': temperature,
      'humidity': humidity,
      'weather': weather,
      'description': description,
      'rainVolume': rainVolume,
      'windSpeed': windSpeed,
    };
  }
}
