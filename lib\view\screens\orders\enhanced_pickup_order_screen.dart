import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class EnhancedPickupOrderScreen extends StatefulWidget {
  final int totalOrders;

  const EnhancedPickupOrderScreen({
    super.key,
    this.totalOrders = 6,
  });

  @override
  State<EnhancedPickupOrderScreen> createState() => _EnhancedPickupOrderScreenState();
}

class _EnhancedPickupOrderScreenState extends State<EnhancedPickupOrderScreen> {
  final List<OrderScanItem> _orders = [
    OrderScanItem(orderId: 'E76592', isScanned: false),
    OrderScanItem(orderId: 'E76593', isScanned: false),
    OrderScanItem(orderId: 'E76594', isScanned: false),
    OrderScanItem(orderId: 'E76595', isScanned: false),
    OrderScanItem(orderId: 'E76596', isScanned: false),
    OrderScanItem(orderId: 'E76597', isScanned: false),
  ];

  bool _isScanning = false;
  bool _isLoading = false;
  int get _scannedCount => _orders.where((order) => order.isScanned).length;

  // Mock order list for compatibility
  List<OrderScanItem> get _orderList => _orders;

  // Simple analytics tracking method
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    // Mock analytics tracking
    print('Analytics: $action - $properties');
  }

  // Simple error tracking method
  void trackError(String error, String details) {
    // Mock error tracking
    print('Error: $error - $details');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Scan Orders',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Progress header
          _buildProgressHeader(),

          // QR Scanner section
          Expanded(
            flex: 3,
            child: _buildQRScannerSection(),
          ),

          // Orders list section
          Expanded(
            flex: 2,
            child: _buildOrdersList(),
          ),

          // Bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Progress text
          Text(
            'Scan QR code to pickup orders',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Progress bar
          Row(
            children: [
              Text(
                '$_scannedCount/${_orders.length}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: LinearProgressIndicator(
                  value: _orders.isEmpty ? 0 : _scannedCount / _orders.length,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                  minHeight: 6,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQRScannerSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Scanner header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  color: AppColors.green,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'QR Code Scanner',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Scanner area
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // QR scanner frame
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.green,
                        width: 3,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Stack(
                      children: [
                        // Corner decorations
                        ...List.generate(4, (index) => _buildCornerDecoration(index)),

                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.qr_code_2,
                                size: 60,
                                color: AppColors.green.withValues(alpha: 0.7),
                              ),
                              const SizedBox(height: 12),
                              const Text(
                                'Align QR code within frame',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Scan button
                  ElevatedButton(
                    onPressed: _isScanning ? null : _simulateScan,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.green,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: _isScanning
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Tap to Scan',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // List header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Orders to Scan (${_orders.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),

          // Orders list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _orders.length,
              itemBuilder: (context, index) {
                final order = _orders[index];
                return _buildOrderItem(order, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: _allScanned ? _proceedToNextStep : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: _allScanned ? AppColors.green : Colors.grey,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
          ),
          child: Text(
            _allScanned ? 'Proceed to Pickup' : 'Scan All Orders',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCornerDecoration(int index) {
    final positions = [
      const Alignment(-1, -1), // Top-left
      const Alignment(1, -1), // Top-right
      const Alignment(-1, 1), // Bottom-left
      const Alignment(1, 1), // Bottom-right
    ];

    return Positioned.fill(
      child: Align(
        alignment: positions[index],
        child: Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.green,
              width: 3,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderItem(OrderScanItem order, int index) {
    final isCompleted = order.isScanned;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.green.withValues(alpha: 0.1) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCompleted ? AppColors.green : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Status icon
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCompleted ? AppColors.green : Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.qr_code_scanner,
              color: Colors.white,
              size: 16,
            ),
          ),

          const SizedBox(width: 12),

          // Order details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order #${order.orderId}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isCompleted ? 'Scanned successfully' : 'Tap to scan',
                  style: TextStyle(
                    fontSize: 12,
                    color: isCompleted ? AppColors.green : Colors.black54,
                    fontWeight: isCompleted ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),

          // Status indicator
          Icon(
            isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isCompleted ? AppColors.green : Colors.grey.shade400,
            size: 20,
          ),
        ],
      ),
    );
  }

  void _simulateScan() {
    setState(() {
      _isScanning = true;
    });

    // Track scan attempt
    trackUserAction('qr_scan_attempted');

    // Simulate scanning delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // Find the first unscanned order
        final unscannedIndex = _orders.indexWhere((order) => !order.isScanned);

        if (unscannedIndex != -1) {
          setState(() {
            _orders[unscannedIndex].isScanned = true;
            _isScanning = false;
          });

          // Track successful scan
          trackUserAction('qr_scan_success', properties: {
            'order_id': _orders[unscannedIndex].orderId,
            'scanned_count': _scannedCount,
            'total_orders': _orders.length,
          });

          // Show success feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Order #${_orders[unscannedIndex].orderId} scanned successfully!'),
              backgroundColor: AppColors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        } else {
          setState(() {
            _isScanning = false;
          });

          // Track scan attempt when all orders are already scanned
          trackUserAction('qr_scan_all_complete');

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('All orders have been scanned!'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    });
  }

  bool get _allScanned => _orders.isNotEmpty && _orders.every((order) => order.isScanned);

  void _proceedToNextStep() {
    Get.toNamed(AppRoutes.orderPickup);
  }
}

class OrderScanItem {
  final String orderId;
  bool isScanned;

  OrderScanItem({
    required this.orderId,
    this.isScanned = false,
  });

  OrderScanItem copyWith({
    String? orderId,
    bool? isScanned,
  }) {
    return OrderScanItem(
      orderId: orderId ?? this.orderId,
      isScanned: isScanned ?? this.isScanned,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderScanItem && other.orderId == orderId && other.isScanned == isScanned;
  }

  @override
  int get hashCode => Object.hash(orderId, isScanned);

  @override
  String toString() {
    return 'OrderScanItem(orderId: $orderId, isScanned: $isScanned)';
  }
}
