import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class EnhancedPickupOrderScreen extends StatefulWidget {
  final int totalOrders;

  const EnhancedPickupOrderScreen({
    super.key,
    this.totalOrders = 6,
  });

  @override
  State<EnhancedPickupOrderScreen> createState() => _EnhancedPickupOrderScreenState();
}

class _EnhancedPickupOrderScreenState extends State<EnhancedPickupOrderScreen> {
  final List<OrderScanItem> _orders = [
    OrderScanItem(orderId: 'E76592', isScanned: false),
    OrderScanItem(orderId: 'E76593', isScanned: false),
    OrderScanItem(orderId: 'E76594', isScanned: false),
    OrderScanItem(orderId: 'E76595', isScanned: false),
    OrderScanItem(orderId: 'E76596', isScanned: false),
    OrderScanItem(orderId: 'E76597', isScanned: false),
  ];

  bool _isScanning = false;
  bool _isLoading = false;
  int get _scannedCount => _orders.where((order) => order.isScanned).length;

  // Mock order list for compatibility
  List<OrderScanItem> get _orderList => _orders;

  // Simple analytics tracking method
  void trackUserAction(String action, {Map<String, dynamic>? properties}) {
    // Mock analytics tracking
    print('Analytics: $action - $properties');
  }

  // Simple error tracking method
  void trackError(String error, String details) {
    // Mock error tracking
    print('Error: $error - $details');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Scan Orders',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Progress header
          _buildProgressHeader(),

          // QR Scanner section
          Expanded(
            flex: 3,
            child: _buildQRScannerSection(),
          ),

          // Orders list section
          Expanded(
            flex: 2,
            child: _buildOrdersList(),
          ),

          // Bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Progress text
          Text(
            'Scan QR code to pickup orders',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Progress bar
          Row(
            children: [
              Text(
                '$_scannedCount/${_orders.length}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: LinearProgressIndicator(
                  value: _orders.isEmpty ? 0 : _scannedCount / _orders.length,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                  minHeight: 6,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQRScannerSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Scanner header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  color: AppColors.green,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'QR Code Scanner',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Scanner area
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // QR scanner frame
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.green,
                        width: 3,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Stack(
                      children: [
                        // Corner decorations
                        ...List.generate(4, (index) => _buildCornerDecoration(index)),

                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.qr_code_2,
                                size: 60,
                                color: AppColors.green.withValues(alpha: 0.7),
                              ),
                              const SizedBox(height: 12),
                              const Text(
                                'Align QR code within frame',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Scan button
                  ElevatedButton(
                    onPressed: _isScanning ? null : _simulateScan,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.green,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: _isScanning
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Tap to Scan',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // List header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Orders to Scan (${_orders.length})',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),

          // Orders list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _orders.length,
              itemBuilder: (context, index) {
                final order = _orders[index];
                return _buildOrderItem(order, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: _allScanned ? _proceedToNextStep : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: _allScanned ? AppColors.green : Colors.grey,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
          ),
          child: Text(
            _allScanned ? 'Proceed to Pickup' : 'Scan All Orders',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCornerDecoration(int index) {
    final positions = [
      const Alignment(-1, -1), // Top-left
      const Alignment(1, -1), // Top-right
      const Alignment(-1, 1), // Bottom-left
      const Alignment(1, 1), // Bottom-right
    ];

    return Positioned.fill(
      child: Align(
        alignment: positions[index],
        child: Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.green,
              width: 3,
            ),
          ),
        ),
      ),
    );
  }

  bool get _allScanned => _orders.isNotEmpty && _orders.every((order) => order.isScanned);

  void _proceedToNextStep() {
    Get.toNamed(AppRoutes.orderPickup);
  }

  Widget _buildOrderCard(Order order, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Order header
          Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            decoration: BoxDecoration(
              color: AppColors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                topRight: Radius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: ResponsiveUtils.spacingS(context),
                    vertical: ResponsiveUtils.spacingXS(context),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.green,
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                  ),
                  child: Text(
                    order.orderId,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 12),
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: ResponsiveUtils.spacingS(context),
                    vertical: ResponsiveUtils.spacingXS(context),
                  ),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(order.priority).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                  ),
                  child: Text(
                    order.priority.displayName,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 10),
                      fontWeight: FontWeight.w500,
                      color: _getPriorityColor(order.priority),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Order details
          Padding(
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Customer info
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Expanded(
                      child: Text(
                        '${order.customer.name} (${order.customer.customerType})',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 14),
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),

                // Address
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Expanded(
                      child: Text(
                        order.customer.address,
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 12),
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),

                // Order details row
                Row(
                  children: [
                    _buildOrderDetailChip(
                      icon: Icons.shopping_cart_outlined,
                      label: '${order.items.length} items',
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    _buildOrderDetailChip(
                      icon: Icons.route_outlined,
                      label: order.formattedDistance,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingS(context)),
                    _buildOrderDetailChip(
                      icon: Icons.currency_rupee,
                      label: order.formattedAmount,
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),

                // Bag info
                Container(
                  padding: EdgeInsets.all(ResponsiveUtils.spacingS(context)),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
                  ),
                  child: Row(
                    children: [
                      _buildBagInfo('Saddle', order.bagInfo.saddleBags),
                      SizedBox(width: ResponsiveUtils.spacingM(context)),
                      _buildBagInfo('Silver', order.bagInfo.silverBags),
                      SizedBox(width: ResponsiveUtils.spacingM(context)),
                      _buildBagInfo('Milk', order.bagInfo.milkPouches),
                      SizedBox(width: ResponsiveUtils.spacingM(context)),
                      _buildBagInfo('Racks', order.bagInfo.totalRacks),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailChip({
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.spacingS(context),
        vertical: ResponsiveUtils.spacingXS(context),
      ),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: ResponsiveUtils.scale(context, 12),
            color: Colors.grey.shade600,
          ),
          SizedBox(width: ResponsiveUtils.spacingXS(context)),
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 10),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBagInfo(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 14),
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: ResponsiveUtils.fontSize(context, 10),
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActionButton() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: ResponsiveUtils.scale(context, 48),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handlePickupOrders,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: ResponsiveUtils.scale(context, 20),
                    height: ResponsiveUtils.scale(context, 20),
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Pickup All Orders',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(context, 16),
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Future<void> _handlePickupOrders() async {
    setState(() => _isLoading = true);

    try {
      // Track pickup action
      trackUserAction('pickup_orders_batch', properties: {
        'order_count': _orderList.length,
        'order_ids': _orderList.map((o) => o.orderId).toList(),
      });

      // Simulate pickup process
      await Future.delayed(const Duration(seconds: 2));

      // Navigate to order picked up screen
      Get.offNamed(AppRoutes.orderPickedUp, arguments: {
        'orders': _orderList,
        'timeSlot': '06 pm - 08 pm',
      });
    } catch (e) {
      debugPrint('❌ Error picking up orders: $e');
      trackError('pickup_orders_error', e.toString());

      Get.snackbar(
        'Error',
        'Failed to pickup orders. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Color _getPriorityColor(OrderPriority priority) {
    switch (priority) {
      case OrderPriority.urgent:
        return Colors.red;
      case OrderPriority.high:
        return Colors.orange;
      case OrderPriority.medium:
        return Colors.blue;
      case OrderPriority.low:
        return Colors.green;
    }
  }

  double _calculateTotalDistance() {
    return _orderList.fold(0.0, (sum, order) => sum + order.distance);
  }

  List<Order> _getDefaultOrders() {
    // Return sample orders if none provided
    return [
      Order(
        orderId: 'E76592',
        batchId: 'BATCH001',
        customer: CustomerInfo(
          customerId: 'CUST001',
          name: 'Prathamesh Chavan',
          phoneNumber: '+91 9876543210',
          address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society, Nerul (w), Navi Mumbai',
          landmark: 'Opposite to Dreamland near Metro Station',
          latitude: 19.0330,
          longitude: 73.0297,
          customerType: 'Gold',
        ),
        items: [
          OrderItem(itemId: 'MILK001', itemName: 'Fresh Milk', quantity: 2, price: 25.0, unit: 'L'),
        ],
        bagInfo: DeliveryBagInfo(saddleBags: 1, silverBags: 1, milkPouches: 2, totalRacks: 12),
        status: OrderStatus.accepted,
        paymentMode: PaymentMode.cash,
        priority: OrderPriority.high,
        totalAmount: 85.0,
        distance: 2.5,
        createdAt: DateTime.now(),
      ),
    ];
  }
}

class OrderScanItem {
  final String orderId;
  bool isScanned;

  OrderScanItem({
    required this.orderId,
    this.isScanned = false,
  });

  OrderScanItem copyWith({
    String? orderId,
    bool? isScanned,
  }) {
    return OrderScanItem(
      orderId: orderId ?? this.orderId,
      isScanned: isScanned ?? this.isScanned,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderScanItem && other.orderId == orderId && other.isScanned == isScanned;
  }

  @override
  int get hashCode => Object.hash(orderId, isScanned);

  @override
  String toString() {
    return 'OrderScanItem(orderId: $orderId, isScanned: $isScanned)';
  }
}
