import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/view/widgets/weather/rain_surge_popup.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_widgets.dart';

/// Rain Surge Banner - appears when it's raining to show extra earnings
class RainSurgeBanner extends StatelessWidget {
  final String title;
  final String amount;
  final String description;
  final VoidCallback? onTap;

  const RainSurgeBanner({
    super.key,
    this.title = "Rain Surge!",
    this.amount = "₹20",
    this.description = "extra per order",
    this.onTap,
  });

  /// Show rain surge popup with details
  void _showRainSurgePopup(BuildContext context) {
    RainSurgePopup.show(
      context,
      title: title,
      amount: amount,
      description: description,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap ?? () => _showRainSurgePopup(context),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Rain icon with exact background specifications
                CachedAssetImage.icon(
                  assetPath: 'assets/icons/rain_icon.png',
                  size: 40.0,
                  fit: BoxFit.contain,
                  errorWidget: const Icon(
                    Icons.cloud_queue, // Cloud with rain icon
                    color: Colors.blue,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 12),

                // Text content - single line to fit 64px height
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        title,
                        style: AppTextTheme.cardTitle.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "Earn $amount $description",
                        style: AppTextTheme.cardSubtitle.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Animated Rain Surge Banner with pulsing effect
class AnimatedRainSurgeBanner extends StatefulWidget {
  final String title;
  final String amount;
  final String description;
  final VoidCallback? onTap;

  const AnimatedRainSurgeBanner({
    super.key,
    this.title = "Rain Surge!",
    this.amount = "₹20",
    this.description = "extra per order",
    this.onTap,
  });

  @override
  State<AnimatedRainSurgeBanner> createState() => _AnimatedRainSurgeBannerState();
}

class _AnimatedRainSurgeBannerState extends State<AnimatedRainSurgeBanner> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  /// Show rain surge popup with details
  void _showRainSurgePopup(BuildContext context) {
    RainSurgePopup.show(
      context,
      title: widget.title,
      amount: widget.amount,
      description: widget.description,
    );
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start the pulsing animation
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: RainSurgeBanner(
            title: widget.title,
            amount: widget.amount,
            description: widget.description,
            onTap: widget.onTap ?? () => _showRainSurgePopup(context),
          ),
        );
      },
    );
  }
}
