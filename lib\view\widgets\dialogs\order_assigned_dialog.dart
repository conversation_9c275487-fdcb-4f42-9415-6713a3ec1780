import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/utils/responsive_utils.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'dart:async';

class OrderAssignedDialog extends StatefulWidget {
  final int totalOrders;
  final double totalEarning;
  final double totalDistance;
  final int saddleBags;
  final int silverBags;
  final VoidCallback? onAcceptOrder;

  const OrderAssignedDialog({
    super.key,
    this.totalOrders = 6,
    this.totalEarning = 125.0,
    this.totalDistance = 5.5,
    this.saddleBags = 9,
    this.silverBags = 10,
    this.onAcceptOrder,
  });

  /// Show the order assigned bottom sheet
  static Future<bool?> show({
    int totalOrders = 6,
    double totalEarning = 125.0,
    double totalDistance = 5.5,
    int saddleBags = 9,
    int silverBags = 10,
    VoidCallback? onAcceptOrder,
  }) async {
    return await Get.bottomSheet<bool>(
      OrderAssignedDialog(
        totalOrders: totalOrders,
        totalEarning: totalEarning,
        totalDistance: totalDistance,
        saddleBags: saddleBags,
        silverBags: silverBags,
        onAcceptOrder: onAcceptOrder,
      ),
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
    );
  }

  @override
  State<OrderAssignedDialog> createState() => _OrderAssignedDialogState();
}

class _OrderAssignedDialogState extends State<OrderAssignedDialog> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  late Timer _timer;
  int _countdown = 10; // Start from 10 seconds

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for the circular progress
    _animationController = AnimationController(
      duration: Duration(seconds: 10),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));

    // Start the countdown timer
    _startCountdown();

    // Start the animation
    _animationController.forward();
  }

  void _startCountdown() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _timer.cancel();
          // Auto-dismiss bottom sheet when countdown reaches 0
          Get.back(result: false);
        }
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Container(
            width: ResponsiveUtils.scale(context, 390),
            height: ResponsiveUtils.scale(context, 522),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag handle for bottom sheet
                Container(
                  margin: EdgeInsets.only(top: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: ResponsiveUtils.scale(context, 16)),

                // Header with icon and title - EXACT MATCH
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Grocery bag icon with exact styling
                      Container(
                        width: ResponsiveUtils.scale(context, 40),
                        height: ResponsiveUtils.scale(context, 40),
                        decoration: BoxDecoration(
                          color: AppColors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.shopping_bag,
                          size: ResponsiveUtils.scale(context, 24),
                          color: AppColors.green,
                        ),
                      ),
                      SizedBox(width: ResponsiveUtils.scale(context, 16)),
                      // Title and subtitle
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              AppStrings.get('orderAssigned'),
                              style: AppTextTheme.cardTitle.copyWith(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              AppStrings.get('orderGettingPacked'),
                              style: AppTextTheme.cardSubtitle.copyWith(
                                fontSize: 14,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Animated circular countdown timer
                      AnimatedBuilder(
                        animation: _animation,
                        builder: (context, child) {
                          return SizedBox(
                            width: ResponsiveUtils.scale(context, 36),
                            height: ResponsiveUtils.scale(context, 36),
                            child: Stack(
                              children: [
                                // Background circle
                                Container(
                                  width: ResponsiveUtils.scale(context, 36),
                                  height: ResponsiveUtils.scale(context, 36),
                                  decoration: BoxDecoration(
                                    color: AppColors.green.withValues(alpha: 0.1),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                // Progress circle
                                SizedBox(
                                  width: ResponsiveUtils.scale(context, 36),
                                  height: ResponsiveUtils.scale(context, 36),
                                  child: CircularProgressIndicator(
                                    value: _animation.value,
                                    strokeWidth: 2,
                                    backgroundColor: Colors.transparent,
                                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.green),
                                  ),
                                ),
                                // Countdown number
                                SizedBox(
                                  width: ResponsiveUtils.scale(context, 36),
                                  height: ResponsiveUtils.scale(context, 36),
                                  child: Center(
                                    child: Text(
                                      _countdown.toString(),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.green,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(height: ResponsiveUtils.scale(context, 16)),

                // Order details - EXACT MATCH layout
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.scale(context, 16)),
                  child: Column(
                    children: [
                      // First row: Total earning and Total order
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.get('totalEarning'),
                                  style: AppTextTheme.cardSubtitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '₹${widget.totalEarning.toInt()}',
                                  style: AppTextTheme.cardTitle.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.get('totalOrder'),
                                  style: AppTextTheme.cardSubtitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '${widget.totalOrders} ${AppStrings.get('ordersCount')}',
                                  style: AppTextTheme.cardTitle.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: ResponsiveUtils.scale(context, 16)),
                      // Second row: Total distance and Total saddle bag
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.get('totalDistance'),
                                  style: AppTextTheme.cardSubtitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '${widget.totalDistance.toStringAsFixed(2)}${AppStrings.get('kmUnit')}',
                                  style: AppTextTheme.cardTitle.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.get('totalSaddleBag'),
                                  style: AppTextTheme.cardSubtitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '${widget.saddleBags} ${AppStrings.get('bagsCount')}',
                                  style: AppTextTheme.cardTitle.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: ResponsiveUtils.scale(context, 16)),
                      // Third row: Total silver bag (single item)
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.get('totalSilverBag'),
                                  style: AppTextTheme.cardSubtitle.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '${widget.silverBags} ${AppStrings.get('bagsCount')}',
                                  style: AppTextTheme.cardTitle.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(child: SizedBox()), // Empty space to maintain layout
                        ],
                      ),
                    ],
                  ),
                ),

                SizedBox(height: ResponsiveUtils.scale(context, 16)),

                // Bell notification icon - positioned to the right
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: ResponsiveUtils.scale(context, 16)),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Container(
                      width: ResponsiveUtils.scale(context, 40),
                      height: ResponsiveUtils.scale(context, 40),
                      decoration: BoxDecoration(
                        color: AppColors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.notifications_active,
                        color: AppColors.green,
                        size: ResponsiveUtils.scale(context, 20),
                      ),
                    ),
                  ),
                ),

                Spacer(),

                // Accept order button
                Padding(
                  padding: EdgeInsets.all(ResponsiveUtils.scale(context, 16)),
                  child: SizedBox(
                    width: double.infinity,
                    height: ResponsiveUtils.scale(context, 56),
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back(result: true);
                        widget.onAcceptOrder?.call();
                        // Navigate to Pickup Order Screen
                        Get.toNamed(AppRoutes.pickupOrder, arguments: {
                          'totalOrders': widget.totalOrders,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(ResponsiveUtils.scale(context, 32)),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppStrings.get('next'),
                            style: AppTextTheme.buttonLarge.copyWith(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: ResponsiveUtils.scale(context, 8)),
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: ResponsiveUtils.scale(context, 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
