import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  late DeliveryOrderItem _currentOrder;

  int _overallRating = 0;
  int _locationAccuracyRating = 0;
  int _customerBehaviorRating = 0;

  final TextEditingController _commentsController = TextEditingController();
  bool _enableLocationUpdates = true;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _currentOrder = args?['currentOrder'] ??
        const DeliveryOrderItem(
          orderId: 'E76592',
          customerName: 'John Doe',
          address: 'Sector 15, Vashi, Navi Mumbai',
          distance: 2.5,
          priority: 1,
          estimatedTime: '15 min',
          paymentMode: 'COD',
          bagCount: 3,
        );
  }

  void _submitFeedback() {
    if (_overallRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide an overall rating')),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      // Show success message
      _showFeedbackSubmittedDialog();
    });
  }

  void _showFeedbackSubmittedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.green, size: 24),
            const SizedBox(width: 8),
            const Text('Feedback Submitted'),
          ],
        ),
        content: const Text('Thank you for your feedback! It helps us improve our service.'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close dialog
              _proceedToNextStep();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Continue', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _proceedToNextStep() {
    // Navigate to order summary or next delivery
    Get.toNamed(AppRoutes.orderSummary, arguments: {
      'currentOrder': _currentOrder,
      'feedbackSubmitted': true,
    });
  }

  void _showLocationPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Updates'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Enable location updates to:'),
            SizedBox(height: 8),
            Text('• Get better delivery routes'),
            Text('• Receive location-based notifications'),
            Text('• Improve delivery time estimates'),
            Text('• Help customers track your location'),
            SizedBox(height: 12),
            Text(
              'You can change this setting anytime in app preferences.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _enableLocationUpdates = false;
              });
              Get.back();
            },
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _enableLocationUpdates = true;
              });
              Get.back();
              _requestLocationPermission();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Enable', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _requestLocationPermission() {
    // Simulate location permission request
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location updates enabled successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          'Delivery Feedback',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Order info header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.green,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order #${_currentOrder.orderId} Delivered',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentOrder.customerName,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    'Completed',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Feedback form
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Overall rating
                  _buildRatingSection(
                    title: 'Overall Delivery Experience',
                    subtitle: 'How was your overall delivery experience?',
                    rating: _overallRating,
                    onRatingChanged: (rating) {
                      setState(() {
                        _overallRating = rating;
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Location accuracy rating
                  _buildRatingSection(
                    title: 'Location Accuracy',
                    subtitle: 'How accurate was the delivery location?',
                    rating: _locationAccuracyRating,
                    onRatingChanged: (rating) {
                      setState(() {
                        _locationAccuracyRating = rating;
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Customer behavior rating
                  _buildRatingSection(
                    title: 'Customer Behavior',
                    subtitle: 'How was the customer interaction?',
                    rating: _customerBehaviorRating,
                    onRatingChanged: (rating) {
                      setState(() {
                        _customerBehaviorRating = rating;
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Comments section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Additional Comments',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Share any additional feedback or suggestions',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _commentsController,
                          maxLines: 4,
                          decoration: const InputDecoration(
                            hintText: 'Type your comments here...',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.all(12),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Location updates toggle
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: AppColors.green,
                          size: 24,
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Enable Location Updates',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Get better routes and notifications',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Switch(
                          value: _enableLocationUpdates,
                          onChanged: (value) {
                            if (value) {
                              _showLocationPermissionDialog();
                            } else {
                              setState(() {
                                _enableLocationUpdates = false;
                              });
                            }
                          },
                          activeColor: AppColors.green,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Submit button
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitFeedback,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _overallRating > 0 ? AppColors.green : Colors.grey,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: _isSubmitting
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Submitting...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Submit Feedback',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSection({
    required String title,
    required String subtitle,
    required int rating,
    required Function(int) onRatingChanged,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final starIndex = index + 1;
              return GestureDetector(
                onTap: () => onRatingChanged(starIndex),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    starIndex <= rating ? Icons.star : Icons.star_border,
                    color: starIndex <= rating ? Colors.amber : Colors.grey.shade400,
                    size: 32,
                  ),
                ),
              );
            }),
          ),
          if (rating > 0)
            Center(
              child: Text(
                _getRatingText(rating),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _getRatingColor(rating),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return '';
    }
  }

  Color _getRatingColor(int rating) {
    switch (rating) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
