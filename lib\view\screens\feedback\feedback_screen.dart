import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/pickup_order_models.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  late DeliveryOrderItem _currentOrder;

  int _overallRating = 0;
  bool? _isLocationCorrect; // null = not selected, true = thumbs up, false = thumbs down
  int _customerBehaviorRating = 0;

  final TextEditingController _commentsController = TextEditingController();
  bool _enableLocationUpdates = true;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    _currentOrder = args?['currentOrder'] ??
        const DeliveryOrderItem(
          orderId: 'E76592',
          customerName: 'John Doe',
          address: 'Sector 15, Vashi, Navi Mumbai',
          distance: 2.5,
          priority: 1,
          estimatedTime: '15 min',
          paymentMode: 'COD',
          bagCount: 3,
        );
  }

  void _submitFeedback() {
    if (_isLocationCorrect == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please answer the location question')),
      );
      return;
    }

    if (_customerBehaviorRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please rate the customer behavior')),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      // Show success message
      _showFeedbackSubmittedDialog();
    });
  }

  void _showFeedbackSubmittedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.green, size: 24),
            const SizedBox(width: 8),
            const Text('Feedback Submitted'),
          ],
        ),
        content: const Text('Thank you for your feedback! It helps us improve our service.'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close dialog
              _proceedToNextStep();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Continue', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _proceedToNextStep() {
    // Navigate to order summary or next delivery
    Get.toNamed(AppRoutes.orderSummary, arguments: {
      'currentOrder': _currentOrder,
      'feedbackSubmitted': true,
    });
  }

  void _showLocationPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Updates'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Enable location updates to:'),
            SizedBox(height: 8),
            Text('• Get better delivery routes'),
            Text('• Receive location-based notifications'),
            Text('• Improve delivery time estimates'),
            Text('• Help customers track your location'),
            SizedBox(height: 12),
            Text(
              'You can change this setting anytime in app preferences.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _enableLocationUpdates = false;
              });
              Get.back();
            },
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _enableLocationUpdates = true;
              });
              Get.back();
              _requestLocationPermission();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.green),
            child: const Text('Enable', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _requestLocationPermission() {
    // Simulate location permission request
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location updates enabled successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: null,
        automaticallyImplyLeading: false,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: GestureDetector(
              onTap: () => Get.back(),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: Colors.grey.shade600,
                  size: 18,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Feedback header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            color: Colors.white,
            child: const Text(
              'Your feedback helps improve the\ndelivery experience',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
                height: 1.3,
              ),
              textAlign: TextAlign.left,
            ),
          ),

          // Feedback form
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Location accuracy section with thumbs up/down
                  _buildLocationAccuracySection(),

                  const SizedBox(height: 32),

                  // Customer behavior rating
                  _buildCustomerBehaviorSection(),
                ],
              ),
            ),
          ),

          // Submit button
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitFeedback,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      (_isLocationCorrect != null && _customerBehaviorRating > 0) ? AppColors.green : Colors.grey,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: _isSubmitting
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Submitting...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Submit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSection({
    required String title,
    required String subtitle,
    required int rating,
    required Function(int) onRatingChanged,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final starIndex = index + 1;
              return GestureDetector(
                onTap: () => onRatingChanged(starIndex),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    starIndex <= rating ? Icons.star : Icons.star_border,
                    color: starIndex <= rating ? Colors.amber : Colors.grey.shade400,
                    size: 32,
                  ),
                ),
              );
            }),
          ),
          if (rating > 0)
            Center(
              child: Text(
                _getRatingText(rating),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _getRatingColor(rating),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return '';
    }
  }

  Color _getRatingColor(int rating) {
    switch (rating) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget _buildCustomerBehaviorSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Customer icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person,
              color: Colors.blue.shade600,
              size: 40,
            ),
          ),
          const SizedBox(height: 16),

          // Question text
          const Text(
            'How was customer\'s behaviour?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Star rating
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              final starIndex = index + 1;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _customerBehaviorRating = starIndex;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    starIndex <= _customerBehaviorRating ? Icons.star : Icons.star_border,
                    color: starIndex <= _customerBehaviorRating ? Colors.amber : Colors.grey.shade400,
                    size: 32,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationAccuracySection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Map icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Icon(
                  Icons.map,
                  color: Colors.blue.shade300,
                  size: 40,
                ),
                Positioned(
                  top: 20,
                  child: Icon(
                    Icons.location_on,
                    color: Colors.red,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Question text
          const Text(
            'Is the customer\'s location correct?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Thumbs up/down buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Thumbs up
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isLocationCorrect = true;
                  });
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _isLocationCorrect == true ? AppColors.green.withValues(alpha: 0.1) : Colors.grey.shade200,
                    shape: BoxShape.circle,
                    border: _isLocationCorrect == true ? Border.all(color: AppColors.green, width: 2) : null,
                  ),
                  child: Icon(
                    Icons.thumb_up,
                    color: _isLocationCorrect == true ? AppColors.green : Colors.grey.shade600,
                    size: 32,
                  ),
                ),
              ),

              // Thumbs down
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isLocationCorrect = false;
                  });
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _isLocationCorrect == false ? Colors.red.withValues(alpha: 0.1) : Colors.grey.shade200,
                    shape: BoxShape.circle,
                    border: _isLocationCorrect == false ? Border.all(color: Colors.red, width: 2) : null,
                  ),
                  child: Icon(
                    Icons.thumb_down,
                    color: _isLocationCorrect == false ? Colors.red : Colors.grey.shade600,
                    size: 32,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
