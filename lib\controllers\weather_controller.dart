import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:kisankonnect_rider/services/weather/weather_service.dart';

/// Weather controller to manage weather state across the app
class WeatherController extends GetxController {
  static WeatherController get instance => Get.find<WeatherController>();

  final WeatherService _weatherService = WeatherService.instance;

  // Observable weather state
  final Rx<WeatherCondition> _currentCondition = WeatherCondition.clear.obs;
  final RxDouble _rainIntensity = 0.0.obs;
  final RxBool _isLoading = false.obs;
  final Rx<WeatherData?> _currentWeatherData = Rx<WeatherData?>(null);

  // Getters
  WeatherCondition get currentCondition => _currentCondition.value;
  double get rainIntensity => _rainIntensity.value;
  bool get isLoading => _isLoading.value;
  WeatherData? get currentWeatherData => _currentWeatherData.value;

  // Weather condition checks
  bool get isHeavyRain => _currentCondition.value == WeatherCondition.heavyRain;
  bool get isRaining => _currentCondition.value == WeatherCondition.lightRain || 
                       _currentCondition.value == WeatherCondition.moderateRain || 
                       _currentCondition.value == WeatherCondition.heavyRain;
  bool get isThunderstorm => _currentCondition.value == WeatherCondition.thunderstorm;
  bool get isCloudy => _currentCondition.value == WeatherCondition.cloudy;
  bool get isClear => _currentCondition.value == WeatherCondition.clear;

  @override
  void onInit() {
    super.onInit();
    _initializeWeather();
  }

  /// Initialize weather monitoring
  void _initializeWeather() {
    // Start weather monitoring service
    _weatherService.startWeatherMonitoring();
    
    // Initial weather fetch
    fetchCurrentWeather();
    
    // Set up periodic weather updates (every 10 minutes)
    _startPeriodicWeatherUpdates();
  }

  /// Fetch current weather data
  Future<void> fetchCurrentWeather() async {
    try {
      _isLoading.value = true;
      
      final weatherData = await _weatherService.getCurrentWeather();
      if (weatherData != null) {
        _currentWeatherData.value = weatherData;
        _updateWeatherState();
        debugPrint('🌦️ Weather updated: ${_currentCondition.value}');
      }
    } catch (e) {
      debugPrint('❌ Weather fetch error: $e');
      // Set default clear weather on error
      _currentCondition.value = WeatherCondition.clear;
      _rainIntensity.value = 0.0;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update weather state based on service data
  void _updateWeatherState() {
    _currentCondition.value = _weatherService.currentCondition;
    _rainIntensity.value = _weatherService.rainIntensity;
  }

  /// Start periodic weather updates
  void _startPeriodicWeatherUpdates() {
    // Update weather every 10 minutes
    Stream.periodic(Duration(minutes: 10)).listen((_) {
      fetchCurrentWeather();
    });
  }

  /// Get weather description for UI
  String get weatherDescription {
    switch (_currentCondition.value) {
      case WeatherCondition.clear:
        return 'Clear sky';
      case WeatherCondition.cloudy:
        return 'Cloudy';
      case WeatherCondition.lightRain:
        return 'Light rain';
      case WeatherCondition.moderateRain:
        return 'Moderate rain';
      case WeatherCondition.heavyRain:
        return 'Heavy rain';
      case WeatherCondition.thunderstorm:
        return 'Thunderstorm';
      default:
        return 'Unknown';
    }
  }

  /// Get weather icon for UI
  String get weatherIcon {
    switch (_currentCondition.value) {
      case WeatherCondition.clear:
        return '☀️';
      case WeatherCondition.cloudy:
        return '☁️';
      case WeatherCondition.lightRain:
        return '🌦️';
      case WeatherCondition.moderateRain:
        return '🌧️';
      case WeatherCondition.heavyRain:
        return '⛈️';
      case WeatherCondition.thunderstorm:
        return '⚡';
      default:
        return '🌤️';
    }
  }

  /// Get safety message for riders
  String get safetyMessage {
    switch (_currentCondition.value) {
      case WeatherCondition.heavyRain:
        return 'Heavy rain detected - Drive carefully and reduce speed!';
      case WeatherCondition.moderateRain:
        return 'Moderate rain - Please drive safely!';
      case WeatherCondition.lightRain:
        return 'Light rain - Be cautious on the road!';
      case WeatherCondition.thunderstorm:
        return 'Thunderstorm warning - Consider taking shelter!';
      case WeatherCondition.cloudy:
        return 'Cloudy weather - Good conditions for delivery!';
      case WeatherCondition.clear:
        return 'Clear weather - Perfect for deliveries!';
      default:
        return 'Stay safe on the road!';
    }
  }

  /// Force weather update (for testing or manual refresh)
  Future<void> refreshWeather() async {
    await fetchCurrentWeather();
  }

  /// Simulate heavy rain for testing
  void simulateHeavyRain() {
    _currentCondition.value = WeatherCondition.heavyRain;
    _rainIntensity.value = 0.8;
    debugPrint('🌧️ Simulating heavy rain for testing');
  }

  /// Simulate clear weather for testing
  void simulateClearWeather() {
    _currentCondition.value = WeatherCondition.clear;
    _rainIntensity.value = 0.0;
    debugPrint('☀️ Simulating clear weather for testing');
  }

  /// Simulate thunderstorm for testing
  void simulateThunderstorm() {
    _currentCondition.value = WeatherCondition.thunderstorm;
    _rainIntensity.value = 1.0;
    debugPrint('⚡ Simulating thunderstorm for testing');
  }
}
