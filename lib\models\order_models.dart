import 'package:flutter/foundation.dart';

/// Order status enumeration
enum OrderStatus {
  assigned('assigned', 'Assigned'),
  accepted('accepted', 'Accepted'),
  pickedUp('picked_up', 'Picked Up'),
  onTheWay('on_the_way', 'On the Way'),
  delivered('delivered', 'Delivered'),
  cancelled('cancelled', 'Cancelled'),
  returned('returned', 'Returned'),
  partiallyDelivered('partially_delivered', 'Partially Delivered');

  const OrderStatus(this.value, this.displayName);
  final String value;
  final String displayName;

  static OrderStatus fromValue(String value) {
    return OrderStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => OrderStatus.assigned,
    );
  }
}

/// Payment mode enumeration
enum PaymentMode {
  cash('cash', 'Cash'),
  qrCode('qr_code', 'QR Code'),
  partialCashQR('partial_cash_qr', 'Partial Cash & QR Code'),
  online('online', 'Online'),
  cod('cod', 'Cash on Delivery');

  const PaymentMode(this.value, this.displayName);
  final String value;
  final String displayName;

  static PaymentMode fromValue(String value) {
    return PaymentMode.values.firstWhere(
      (mode) => mode.value == value,
      orElse: () => PaymentMode.cash,
    );
  }
}

/// Order priority enumeration
enum OrderPriority {
  low('low', 'Low', 1),
  medium('medium', 'Medium', 2),
  high('high', 'High', 3),
  urgent('urgent', 'Urgent', 4);

  const OrderPriority(this.value, this.displayName, this.level);
  final String value;
  final String displayName;
  final int level;

  static OrderPriority fromValue(String value) {
    return OrderPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => OrderPriority.medium,
    );
  }
}

/// Customer information model
class CustomerInfo {
  final String customerId;
  final String name;
  final String phoneNumber;
  final String address;
  final String landmark;
  final double latitude;
  final double longitude;
  final String customerType; // Gold, Silver, Regular
  final String? specialInstructions;

  CustomerInfo({
    required this.customerId,
    required this.name,
    required this.phoneNumber,
    required this.address,
    required this.landmark,
    required this.latitude,
    required this.longitude,
    required this.customerType,
    this.specialInstructions,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      customerId: json['customer_id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      phoneNumber: json['phone_number']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      landmark: json['landmark']?.toString() ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      customerType: json['customer_type']?.toString() ?? 'Regular',
      specialInstructions: json['special_instructions']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customer_id': customerId,
      'name': name,
      'phone_number': phoneNumber,
      'address': address,
      'landmark': landmark,
      'latitude': latitude,
      'longitude': longitude,
      'customer_type': customerType,
      'special_instructions': specialInstructions,
    };
  }
}

/// Order item model
class OrderItem {
  final String itemId;
  final String itemName;
  final int quantity;
  final double price;
  final String unit;
  final String? category;

  OrderItem({
    required this.itemId,
    required this.itemName,
    required this.quantity,
    required this.price,
    required this.unit,
    this.category,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      itemId: json['item_id']?.toString() ?? '',
      itemName: json['item_name']?.toString() ?? '',
      quantity: json['quantity'] ?? 0,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      unit: json['unit']?.toString() ?? '',
      category: json['category']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item_id': itemId,
      'item_name': itemName,
      'quantity': quantity,
      'price': price,
      'unit': unit,
      'category': category,
    };
  }

  double get totalPrice => quantity * price;
}

/// Delivery bag information
class DeliveryBagInfo {
  final int saddleBags;
  final int silverBags;
  final int milkPouches;
  final int totalRacks;

  DeliveryBagInfo({
    required this.saddleBags,
    required this.silverBags,
    required this.milkPouches,
    required this.totalRacks,
  });

  factory DeliveryBagInfo.fromJson(Map<String, dynamic> json) {
    return DeliveryBagInfo(
      saddleBags: json['saddle_bags'] ?? 0,
      silverBags: json['silver_bags'] ?? 0,
      milkPouches: json['milk_pouches'] ?? 0,
      totalRacks: json['total_racks'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'saddle_bags': saddleBags,
      'silver_bags': silverBags,
      'milk_pouches': milkPouches,
      'total_racks': totalRacks,
    };
  }
}

/// Main order model
class Order {
  final String orderId;
  final String batchId;
  final CustomerInfo customer;
  final List<OrderItem> items;
  final DeliveryBagInfo bagInfo;
  final OrderStatus status;
  final PaymentMode paymentMode;
  final OrderPriority priority;
  final double totalAmount;
  final double distance;
  final DateTime createdAt;
  final DateTime? scheduledDeliveryTime;
  final DateTime? pickedUpAt;
  final DateTime? deliveredAt;
  final String? deliveryNotes;
  final String? cancellationReason;
  final double? cashReceived;
  final String? qrTransactionId;

  Order({
    required this.orderId,
    required this.batchId,
    required this.customer,
    required this.items,
    required this.bagInfo,
    required this.status,
    required this.paymentMode,
    required this.priority,
    required this.totalAmount,
    required this.distance,
    required this.createdAt,
    this.scheduledDeliveryTime,
    this.pickedUpAt,
    this.deliveredAt,
    this.deliveryNotes,
    this.cancellationReason,
    this.cashReceived,
    this.qrTransactionId,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      orderId: json['order_id']?.toString() ?? '',
      batchId: json['batch_id']?.toString() ?? '',
      customer: CustomerInfo.fromJson(json['customer'] ?? {}),
      items: (json['items'] as List?)
          ?.map((item) => OrderItem.fromJson(item))
          .toList() ?? [],
      bagInfo: DeliveryBagInfo.fromJson(json['bag_info'] ?? {}),
      status: OrderStatus.fromValue(json['status']?.toString() ?? 'assigned'),
      paymentMode: PaymentMode.fromValue(json['payment_mode']?.toString() ?? 'cash'),
      priority: OrderPriority.fromValue(json['priority']?.toString() ?? 'medium'),
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.tryParse(json['created_at']?.toString() ?? '') ?? DateTime.now(),
      scheduledDeliveryTime: json['scheduled_delivery_time'] != null
          ? DateTime.tryParse(json['scheduled_delivery_time'])
          : null,
      pickedUpAt: json['picked_up_at'] != null
          ? DateTime.tryParse(json['picked_up_at'])
          : null,
      deliveredAt: json['delivered_at'] != null
          ? DateTime.tryParse(json['delivered_at'])
          : null,
      deliveryNotes: json['delivery_notes']?.toString(),
      cancellationReason: json['cancellation_reason']?.toString(),
      cashReceived: (json['cash_received'] as num?)?.toDouble(),
      qrTransactionId: json['qr_transaction_id']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'batch_id': batchId,
      'customer': customer.toJson(),
      'items': items.map((item) => item.toJson()).toList(),
      'bag_info': bagInfo.toJson(),
      'status': status.value,
      'payment_mode': paymentMode.value,
      'priority': priority.value,
      'total_amount': totalAmount,
      'distance': distance,
      'created_at': createdAt.toIso8601String(),
      'scheduled_delivery_time': scheduledDeliveryTime?.toIso8601String(),
      'picked_up_at': pickedUpAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'delivery_notes': deliveryNotes,
      'cancellation_reason': cancellationReason,
      'cash_received': cashReceived,
      'qr_transaction_id': qrTransactionId,
    };
  }

  /// Create a copy of the order with updated fields
  Order copyWith({
    String? orderId,
    String? batchId,
    CustomerInfo? customer,
    List<OrderItem>? items,
    DeliveryBagInfo? bagInfo,
    OrderStatus? status,
    PaymentMode? paymentMode,
    OrderPriority? priority,
    double? totalAmount,
    double? distance,
    DateTime? createdAt,
    DateTime? scheduledDeliveryTime,
    DateTime? pickedUpAt,
    DateTime? deliveredAt,
    String? deliveryNotes,
    String? cancellationReason,
    double? cashReceived,
    String? qrTransactionId,
  }) {
    return Order(
      orderId: orderId ?? this.orderId,
      batchId: batchId ?? this.batchId,
      customer: customer ?? this.customer,
      items: items ?? this.items,
      bagInfo: bagInfo ?? this.bagInfo,
      status: status ?? this.status,
      paymentMode: paymentMode ?? this.paymentMode,
      priority: priority ?? this.priority,
      totalAmount: totalAmount ?? this.totalAmount,
      distance: distance ?? this.distance,
      createdAt: createdAt ?? this.createdAt,
      scheduledDeliveryTime: scheduledDeliveryTime ?? this.scheduledDeliveryTime,
      pickedUpAt: pickedUpAt ?? this.pickedUpAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      deliveryNotes: deliveryNotes ?? this.deliveryNotes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cashReceived: cashReceived ?? this.cashReceived,
      qrTransactionId: qrTransactionId ?? this.qrTransactionId,
    );
  }

  /// Check if order can be picked up
  bool get canPickUp => status == OrderStatus.accepted;

  /// Check if order is ready for delivery
  bool get canStartDelivery => status == OrderStatus.pickedUp;

  /// Check if order can be delivered
  bool get canDeliver => status == OrderStatus.onTheWay;

  /// Get formatted distance
  String get formattedDistance => '${distance.toStringAsFixed(1)} km';

  /// Get formatted total amount
  String get formattedAmount => '₹${totalAmount.toStringAsFixed(2)}';
}
