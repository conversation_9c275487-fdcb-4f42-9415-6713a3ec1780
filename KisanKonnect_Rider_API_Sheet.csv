Sheet,KisanKonnect Internal APIs
API Name,Endpoint,Method,Base URL,Purpose,Request Parameters,Response Format,Implementation Date,Status,CURL Request Example
Rider Login,/Rider/FE_RidersLoginNewV1,GET,http://knet.kisankonnect.com/SRIT3O/api,User authentication and OTP verification,MobileNo OTP GCMID IMEI,JSON,2024-06-21,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_RidersLoginNewV1?MobileNo=9876543210&OTP=123456&GCMID=device_gcm_id&IMEI=device_imei"""
WhatsApp OTP,/Rider/FE_SendWhatsAppOTP,POST,http://knet.kisankonnect.com/SRIT3O/api,Send OTP via WhatsApp,MobileNo OTP Channel TemplateName,JSON,2024-07-02,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_SendWhatsAppOTP"" -H ""Content-Type: application/json"" -d '{""MobileNo"":""9876543210"",""OTP"":""123456"",""Channel"":""whatsapp"",""TemplateName"":""otp_template""}'"
Rider Details,/Rider/FE_GetRiderDetails,GET,http://knet.kisankonnect.com/SRIT3O/api,Get rider profile information,mob,JSON,2024-06-23,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_GetRiderDetails?mob=9876543210"""
User Info After Login,/Rider/FE_SRInfoAfterLogin,GET,http://knet.kisankonnect.com/SRIT3O/api,Get detailed user info after login,MobileNo OTP GCMID IMEI,JSON,2024-06-24,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_SRInfoAfterLogin?MobileNo=9876543210&OTP=123456&GCMID=device_gcm_id&IMEI=device_imei"""
Register Profile,/Rider/IN_RiderRegistration,POST,http://knet.kisankonnect.com/SRIT3O/api,Register rider profile,FirstName LastName MobileNo EmailID DOB etc,JSON,2024-06-25,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/IN_RiderRegistration"" -H ""Content-Type: application/json"" -d '{""FirstName"":""John"",""LastName"":""Doe"",""MobileNo"":""9876543210"",""EmailID"":""<EMAIL>""}'"
Register Documents,/Rider/RiderRegisterationInsertDocument,POST,http://knet.kisankonnect.com/SRIT3O/api,Upload rider documents,DocumentType DocumentImage UserId,Multipart,2024-06-26,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/RiderRegisterationInsertDocument"" -F ""DocumentType=1"" -F ""DocumentImage=@document.jpg"" -F ""UserId=12345"""
Register Work Details,/Rider/RiderRegisterationInsertWork,POST,http://knet.kisankonnect.com/SRIT3O/api,Register work preferences,VehicleType RiderType ShiftType UserId,JSON,2024-06-27,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/RiderRegisterationInsertWork"" -H ""Content-Type: application/json"" -d '{""VehicleType"":1,""RiderType"":2,""ShiftType"":1,""UserId"":12345}'"
Common Data,/Rider/FE_LoginCommonAPI,GET,http://knet.kisankonnect.com/SRIT3O/api,Get common data for registration,Lat Lng,JSON,2024-06-28,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_LoginCommonAPI?Lat=19.0760&Lng=72.8777"""
Dashboard Story Banner,/Rider/FE_DashboardStoryBanner,GET,http://knet.kisankonnect.com/SRIT3O/api,Get dashboard story banners,UserId,JSON,2024-07-01,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_DashboardStoryBanner?UserId=12345"""
Current Rider Progress,/Rider/FE_CurrentRiderProgressV1,GET,http://knet.kisankonnect.com/SRIT3O/api,Get current rider progress,UserId,JSON,2024-07-02,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_CurrentRiderProgressV1?UserId=12345"""
Weekly Earnings,/Rider/FE_RiderEarining_WeeklyNew,GET,http://knet.kisankonnect.com/SRIT3O/api,Get weekly earnings data,Date SRID KFHID CDCtype,JSON,2024-07-03,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_RiderEarining_WeeklyNew?Date=2024-07-03&SRID=12345&KFHID=1&CDCtype=1"""
Cash Balance,/Rider/FE_KFHRiderCODLimit,GET,http://knet.kisankonnect.com/SRIT3O/api,Get rider cash balance/COD limit,Userid,JSON,2024-07-04,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_KFHRiderCODLimit?Userid=12345"""
Service Level Report,/Rider/FE_KFHRiderServiceLevelReport,GET,http://knet.kisankonnect.com/SRIT3O/api,Get service level reports,UserId DateFrom DateTo,JSON,2024-07-05,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_KFHRiderServiceLevelReport?UserId=12345&DateFrom=2024-07-01&DateTo=2024-07-05"""
Current Rider Status,/Rider/FE_CurrentRiderStatus,GET,http://knet.kisankonnect.com/SRIT3O/api,Get current rider online/offline status,UserId,JSON,2024-07-06,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_CurrentRiderStatus?UserId=12345"""
Rider Available Status,/Rider/FE_RiderAvailableStatus,POST,http://knet.kisankonnect.com/SRIT3O/api,Update rider availability status,DeliveryDate Userid Remark DCID CDCtype Status,JSON,2024-07-07,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_RiderAvailableStatus"" -H ""Content-Type: application/json"" -d '{""DeliveryDate"":""2024-07-07"",""Userid"":""12345"",""Remark"":""Available"",""DCID"":""1"",""CDCtype"":""1"",""Status"":""1""}'"
Rider Break Time,/Rider/FE_RiderBreakTime,POST,http://knet.kisankonnect.com/SRIT3O/api,Manage rider break time,UserId BreakType StartTime EndTime,JSON,2024-07-08,Active,"curl -X POST ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_RiderBreakTime"" -H ""Content-Type: application/json"" -d '{""UserId"":""12345"",""BreakType"":""lunch"",""StartTime"":""13:00"",""EndTime"":""14:00""}'"
Refer Earn Banner,/Rider/FE_ReferEarnBanner,GET,http://knet.kisankonnect.com/SRIT3O/api,Get referral and earning banners,UserId,JSON,2024-07-09,Active,"curl -X GET ""http://knet.kisankonnect.com/SRIT3O/api/Rider/FE_ReferEarnBanner?UserId=12345"""

Sheet,External API Integrations
Service Name,API Endpoint,Method,Purpose,Provider,Request Format,Implementation Date,Status,CURL Request Example
IFSC Verification,/{ifsc_code},GET,Verify bank IFSC codes,Razorpay,REST API,2024-06-29,Active,"curl -X GET ""https://ifsc.razorpay.com/SBIN0000001"" -H ""Accept: application/json"""
Twilio WhatsApp,/Accounts/{AccountSid}/Messages.json,POST,Send WhatsApp messages,Twilio,Form Data,2024-07-02,Configured,"curl -X POST ""https://api.twilio.com/2010-04-01/Accounts/YOUR_ACCOUNT_SID/Messages.json"" -u YOUR_ACCOUNT_SID:YOUR_AUTH_TOKEN -d ""From=whatsapp:+***********"" -d ""To=whatsapp:+************"" -d ""Body=Your OTP is: 123456"""
MessageBird WhatsApp,/conversations,POST,Send WhatsApp messages,MessageBird,JSON,2024-07-02,Configured,"curl -X POST ""https://conversations.messagebird.com/v1/conversations"" -H ""Authorization: AccessKey YOUR_API_KEY"" -H ""Content-Type: application/json"" -d '{""to"":""************"",""type"":""text"",""content"":{""text"":""Your OTP is: 123456""},""channelId"":""YOUR_CHANNEL_ID""}'"
Gupshup WhatsApp,/msg,POST,Send WhatsApp messages,Gupshup,Form Data,2024-07-02,Configured,"curl -X POST ""https://api.gupshup.io/sm/api/v1/msg"" -H ""apikey: YOUR_API_KEY"" -H ""Content-Type: application/x-www-form-urlencoded"" -d ""channel=whatsapp&source=YOUR_APP_NAME&destination=************&message=Your OTP is: 123456"""
Firebase Analytics,/events,POST,Track user events,Google Firebase,JSON,2024-06-21,Active,"curl -X POST ""https://www.google-analytics.com/mp/collect?measurement_id=G-XXXXXXXXXX&api_secret=YOUR_API_SECRET"" -H ""Content-Type: application/json"" -d '{""client_id"":""user123"",""events"":[{""name"":""login"",""parameters"":{""method"":""otp""}}]}'"
Microsoft Clarity,/events,POST,User behavior analytics,Microsoft,JSON,2024-06-21,Active,"curl -X POST ""https://www.clarity.ms/api/events"" -H ""Content-Type: application/json"" -H ""Authorization: Bearer YOUR_API_KEY"" -d '{""events"":[{""type"":""page_view"",""url"":""/dashboard"",""timestamp"":""2024-07-21T10:00:00Z""}]}'"
Google Maps Geocoding,/maps/api/geocode/json,GET,Convert addresses to coordinates,Google Maps,REST API,2024-06-30,Active,"curl -X GET ""https://maps.googleapis.com/maps/api/geocode/json?address=Mumbai&key=YOUR_API_KEY"""
Google Maps Places,/maps/api/place/nearbysearch/json,GET,Find nearby places,Google Maps,REST API,2024-06-30,Active,"curl -X GET ""https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=19.0760,72.8777&radius=1000&type=restaurant&key=YOUR_API_KEY"""
Device Info,/device/info,GET,Get device information,Device Info Plus,Local API,2024-06-21,Active,Local Flutter plugin - no CURL needed
Local Auth,/biometric/authenticate,POST,Biometric authentication,Local Auth,Local API,2024-06-23,Active,Local Flutter plugin - no CURL needed

Sheet,API Request Headers
API Category,Common Headers,Authentication,Content Type,Additional Headers,Example
KisanKonnect APIs,Accept: application/json,None (Query params),application/json,User-Agent: KisanKonnect-Rider/1.0.0,"curl -H ""Accept: application/json"" -H ""User-Agent: KisanKonnect-Rider/1.0.0"""
WhatsApp APIs,Accept: application/json,API Key in header,application/json,Authorization: Bearer/AccessKey,"curl -H ""Authorization: AccessKey YOUR_API_KEY"" -H ""Content-Type: application/json"""
Firebase APIs,Accept: application/json,API Secret in URL,application/json,None,"curl -H ""Content-Type: application/json"""
External APIs,Accept: application/json,API Key in header/URL,application/json,Varies by provider,"curl -H ""Accept: application/json"""

Sheet,API Response Formats
API Name,Success Response,Error Response,Status Codes,Response Fields,Example Response
Rider Login,"{""status"":""success"",""data"":{...}}","{""status"":""error"",""message"":""...""}","200, 400, 401, 500","status, data, message, error","{""status"":""success"",""data"":{""userId"":""12345"",""name"":""John Doe""}}"
WhatsApp OTP,"{""success"":true,""messageId"":""...""}","{""success"":false,""error"":""...""}","200, 400, 429, 500","success, messageId, error","{""success"":true,""messageId"":""msg_123456""}"
IFSC Verification,"{""BANK"":""State Bank of India"",""IFSC"":""SBIN0000001""}","{""error"":""Invalid IFSC code""}","200, 404, 500","BANK, IFSC, BRANCH, ADDRESS","{""BANK"":""State Bank of India"",""IFSC"":""SBIN0000001"",""BRANCH"":""Mumbai Main""}"
Firebase Analytics,"{""validationMessages"":[]}","{""error"":{""message"":""...""}}","200, 400, 403","validationMessages, error","{""validationMessages"":[]}"

Sheet,API Error Handling
API Name,Common Errors,Error Codes,Retry Strategy,Fallback Action,Implementation
Rider Login,Invalid OTP Invalid Mobile,AUTH_001 AUTH_002,3 retries with exponential backoff,Show error message,ErrorHandler.handleApiError()
WhatsApp OTP,Rate limit exceeded Service unavailable,WHATSAPP_001 WHATSAPP_002,2 retries then fallback to SMS,Send SMS OTP,Automatic fallback implemented
IFSC Verification,Invalid IFSC Network timeout,IFSC_001 IFSC_002,2 retries,Manual entry allowed,Try-catch with user feedback
External APIs,Network error API key invalid,NET_001 AUTH_003,Exponential backoff,Graceful degradation,Centralized error handling

Sheet,API Security
API Type,Authentication Method,Data Encryption,Rate Limiting,Security Headers,Implementation
KisanKonnect APIs,Query parameters (OTP),HTTPS only,Server-side,Standard headers,Environment-based URLs
WhatsApp APIs,API Key in header,HTTPS + API encryption,Provider-managed,Authorization headers,Secure key storage
Firebase APIs,API Secret,HTTPS + Firebase security,Google-managed,Standard headers,Environment configuration
External APIs,Various (API keys),HTTPS,Provider-managed,Varies,Secure configuration

Sheet,API Performance
API Name,Average Response Time,Timeout Settings,Caching Strategy,Optimization,Monitoring
Rider Login,500ms,30 seconds,No caching,Connection pooling,Error tracking
WhatsApp OTP,1000ms,15 seconds,No caching,Retry logic,Delivery tracking
IFSC Verification,300ms,10 seconds,Memory cache (15 min),Local caching,Response time tracking
Dashboard APIs,800ms,30 seconds,Memory cache (5 min),Data pagination,Performance monitoring
External APIs,Variable,10-30 seconds,Provider-dependent,Connection reuse,Error rate monitoring
