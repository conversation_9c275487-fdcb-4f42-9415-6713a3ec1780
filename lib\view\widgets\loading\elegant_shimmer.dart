import 'package:flutter/material.dart';

/// Elegant shimmer effect like Zomato/Swiggy
class ElegantShimmer extends StatefulWidget {
  final Widget child;
  final bool enabled;
  final Color baseColor;
  final Color highlightColor;
  final Duration duration;

  const ElegantShimmer({
    super.key,
    required this.child,
    this.enabled = true,
    this.baseColor = const Color(0xFFF5F5F5),
    this.highlightColor = const Color(0xFFFFFFFF),
    this.duration = const Duration(milliseconds: 1200),
  });

  @override
  State<ElegantShimmer> createState() => _ElegantShimmerState();
}

class _ElegantShimmerState extends State<ElegantShimmer> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );
    if (widget.enabled) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(ElegantShimmer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: [
                0.0,
                0.5,
                1.0,
              ],
              transform: GradientRotation(_animation.value * 0.5),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Elegant shimmer placeholder components
class ElegantShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius borderRadius;
  final EdgeInsetsGeometry? margin;
  final Color? color;

  const ElegantShimmerBox({
    super.key,
    this.width = double.infinity,
    this.height = 16.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.margin,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color ?? const Color(0xFFF5F5F5),
        borderRadius: borderRadius,
      ),
    );
  }
}

class ElegantShimmerCircle extends StatelessWidget {
  final double size;
  final EdgeInsetsGeometry? margin;
  final Color? color;

  const ElegantShimmerCircle({
    super.key,
    required this.size,
    this.margin,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color ?? const Color(0xFFF5F5F5),
        shape: BoxShape.circle,
      ),
    );
  }
}

class ElegantShimmerLine extends StatelessWidget {
  final double width;
  final double height;
  final EdgeInsetsGeometry? margin;
  final Color? color;

  const ElegantShimmerLine({
    super.key,
    this.width = double.infinity,
    this.height = 12.0,
    this.margin,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color ?? const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(height / 2),
      ),
    );
  }
}

/// Card shimmer for dashboard sections
class ElegantCardShimmer extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final List<Widget>? children;

  const ElegantCardShimmer({
    super.key,
    this.height = 120,
    this.margin,
    this.padding = const EdgeInsets.all(16),
    this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: padding,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: children != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children!,
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ElegantShimmerLine(width: 120, height: 16),
                const SizedBox(height: 12),
                ElegantShimmerLine(width: double.infinity, height: 14),
                const SizedBox(height: 8),
                ElegantShimmerLine(width: 200, height: 14),
                const Spacer(),
                Row(
                  children: [
                    ElegantShimmerBox(width: 80, height: 24, borderRadius: BorderRadius.circular(12)),
                    const Spacer(),
                    ElegantShimmerCircle(size: 32),
                  ],
                ),
              ],
            ),
    );
  }
}
