import 'package:flutter/foundation.dart';

/// Order pickup item model
class PickupOrderItem {
  final String orderId;
  final int columnNumber;
  final int saddleBags;
  final int silverBags;
  final int milkPouches;
  final bool isScanned;
  final List<String> scannedSaddleBags;
  final List<String> scannedSilverBags;
  final List<String> scannedMilkPouches;

  const PickupOrderItem({
    required this.orderId,
    required this.columnNumber,
    required this.saddleBags,
    required this.silverBags,
    required this.milkPouches,
    this.isScanned = false,
    this.scannedSaddleBags = const [],
    this.scannedSilverBags = const [],
    this.scannedMilkPouches = const [],
  });

  /// Check if order should be displayed (has milk pouches or silver bags)
  bool get shouldDisplay => milkPouches > 0 || silverBags > 0;

  /// Check if all items are scanned
  bool get isCompletelyScanned {
    return scannedSaddleBags.length == saddleBags &&
        scannedSilverBags.length == silverBags &&
        scannedMilkPouches.length == milkPouches;
  }

  /// Get current scanning phase
  ScanningPhase get currentScanningPhase {
    if (scannedSaddleBags.length < saddleBags) {
      return ScanningPhase.saddleBags;
    } else if (scannedSilverBags.length < silverBags) {
      return ScanningPhase.silverBags;
    } else if (scannedMilkPouches.length < milkPouches) {
      return ScanningPhase.milkPouches;
    }
    return ScanningPhase.completed;
  }

  /// Get scanning progress text
  String get scanningProgressText {
    switch (currentScanningPhase) {
      case ScanningPhase.saddleBags:
        return 'Scan Saddle Bags (${scannedSaddleBags.length}/$saddleBags)';
      case ScanningPhase.silverBags:
        return 'Scan Silver Bags (${scannedSilverBags.length}/$silverBags)';
      case ScanningPhase.milkPouches:
        return 'Scan Milk Pouches (${scannedMilkPouches.length}/$milkPouches)';
      case ScanningPhase.completed:
        return 'All Items Scanned ✓';
    }
  }

  PickupOrderItem copyWith({
    String? orderId,
    int? columnNumber,
    int? saddleBags,
    int? silverBags,
    int? milkPouches,
    bool? isScanned,
    List<String>? scannedSaddleBags,
    List<String>? scannedSilverBags,
    List<String>? scannedMilkPouches,
  }) {
    return PickupOrderItem(
      orderId: orderId ?? this.orderId,
      columnNumber: columnNumber ?? this.columnNumber,
      saddleBags: saddleBags ?? this.saddleBags,
      silverBags: silverBags ?? this.silverBags,
      milkPouches: milkPouches ?? this.milkPouches,
      isScanned: isScanned ?? this.isScanned,
      scannedSaddleBags: scannedSaddleBags ?? this.scannedSaddleBags,
      scannedSilverBags: scannedSilverBags ?? this.scannedSilverBags,
      scannedMilkPouches: scannedMilkPouches ?? this.scannedMilkPouches,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'columnNumber': columnNumber,
      'saddleBags': saddleBags,
      'silverBags': silverBags,
      'milkPouches': milkPouches,
      'isScanned': isScanned,
      'scannedSaddleBags': scannedSaddleBags,
      'scannedSilverBags': scannedSilverBags,
      'scannedMilkPouches': scannedMilkPouches,
    };
  }

  factory PickupOrderItem.fromJson(Map<String, dynamic> json) {
    return PickupOrderItem(
      orderId: json['orderId'] ?? '',
      columnNumber: json['columnNumber'] ?? 0,
      saddleBags: json['saddleBags'] ?? 0,
      silverBags: json['silverBags'] ?? 0,
      milkPouches: json['milkPouches'] ?? 0,
      isScanned: json['isScanned'] ?? false,
      scannedSaddleBags: List<String>.from(json['scannedSaddleBags'] ?? []),
      scannedSilverBags: List<String>.from(json['scannedSilverBags'] ?? []),
      scannedMilkPouches: List<String>.from(json['scannedMilkPouches'] ?? []),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PickupOrderItem &&
        other.orderId == orderId &&
        other.columnNumber == columnNumber &&
        other.saddleBags == saddleBags &&
        other.silverBags == silverBags &&
        other.milkPouches == milkPouches &&
        other.isScanned == isScanned &&
        listEquals(other.scannedSaddleBags, scannedSaddleBags) &&
        listEquals(other.scannedSilverBags, scannedSilverBags) &&
        listEquals(other.scannedMilkPouches, scannedMilkPouches);
  }

  @override
  int get hashCode {
    return Object.hash(
      orderId,
      columnNumber,
      saddleBags,
      silverBags,
      milkPouches,
      isScanned,
      Object.hashAll(scannedSaddleBags),
      Object.hashAll(scannedSilverBags),
      Object.hashAll(scannedMilkPouches),
    );
  }

  @override
  String toString() {
    return 'PickupOrderItem(orderId: $orderId, columnNumber: $columnNumber, '
        'saddleBags: $saddleBags, silverBags: $silverBags, milkPouches: $milkPouches, '
        'isScanned: $isScanned)';
  }
}

/// Scanning phases enum
enum ScanningPhase {
  saddleBags,
  silverBags,
  milkPouches,
  completed,
}

/// Pickup session model
class PickupSession {
  final String sessionId;
  final List<PickupOrderItem> orders;
  final DateTime startTime;
  final int timerDuration; // in seconds
  final bool isTimerActive;
  final bool isCompleted;

  const PickupSession({
    required this.sessionId,
    required this.orders,
    required this.startTime,
    this.timerDuration = 45,
    this.isTimerActive = true,
    this.isCompleted = false,
  });

  /// Get orders that should be displayed
  List<PickupOrderItem> get displayableOrders {
    return orders.where((order) => order.shouldDisplay).toList();
  }

  /// Get total scanned orders
  int get scannedOrdersCount {
    return orders.where((order) => order.isCompletelyScanned).length;
  }

  /// Check if all orders are scanned
  bool get allOrdersScanned {
    return displayableOrders.every((order) => order.isCompletelyScanned);
  }

  /// Get next order to scan
  PickupOrderItem? get nextOrderToScan {
    return displayableOrders.firstWhere(
      (order) => !order.isCompletelyScanned,
      orElse: () => displayableOrders.first,
    );
  }

  PickupSession copyWith({
    String? sessionId,
    List<PickupOrderItem>? orders,
    DateTime? startTime,
    int? timerDuration,
    bool? isTimerActive,
    bool? isCompleted,
  }) {
    return PickupSession(
      sessionId: sessionId ?? this.sessionId,
      orders: orders ?? this.orders,
      startTime: startTime ?? this.startTime,
      timerDuration: timerDuration ?? this.timerDuration,
      isTimerActive: isTimerActive ?? this.isTimerActive,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'orders': orders.map((order) => order.toJson()).toList(),
      'startTime': startTime.toIso8601String(),
      'timerDuration': timerDuration,
      'isTimerActive': isTimerActive,
      'isCompleted': isCompleted,
    };
  }

  factory PickupSession.fromJson(Map<String, dynamic> json) {
    return PickupSession(
      sessionId: json['sessionId'] ?? '',
      orders: (json['orders'] as List?)?.map((orderJson) => PickupOrderItem.fromJson(orderJson)).toList() ?? [],
      startTime: DateTime.parse(json['startTime'] ?? DateTime.now().toIso8601String()),
      timerDuration: json['timerDuration'] ?? 45,
      isTimerActive: json['isTimerActive'] ?? true,
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}

/// Scan result model
class ScanResult {
  final String qrCode;
  final String orderId;
  final String itemType; // 'saddle_bag', 'silver_bag', 'milk_pouch'
  final DateTime scannedAt;
  final bool isValid;
  final String? errorMessage;

  const ScanResult({
    required this.qrCode,
    required this.orderId,
    required this.itemType,
    required this.scannedAt,
    this.isValid = true,
    this.errorMessage,
  });

  Map<String, dynamic> toJson() {
    return {
      'qrCode': qrCode,
      'orderId': orderId,
      'itemType': itemType,
      'scannedAt': scannedAt.toIso8601String(),
      'isValid': isValid,
      'errorMessage': errorMessage,
    };
  }

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      qrCode: json['qrCode'] ?? '',
      orderId: json['orderId'] ?? '',
      itemType: json['itemType'] ?? '',
      scannedAt: DateTime.parse(json['scannedAt'] ?? DateTime.now().toIso8601String()),
      isValid: json['isValid'] ?? true,
      errorMessage: json['errorMessage'],
    );
  }
}

/// Delivery order item model for order picked up screen
class DeliveryOrderItem {
  final String orderId;
  final String customerName;
  final String address;
  final double distance;
  final int priority;
  final String estimatedTime;
  final String paymentMode;
  final int bagCount;
  final bool isSelected;

  const DeliveryOrderItem({
    required this.orderId,
    required this.customerName,
    required this.address,
    required this.distance,
    required this.priority,
    required this.estimatedTime,
    required this.paymentMode,
    required this.bagCount,
    this.isSelected = false,
  });

  DeliveryOrderItem copyWith({
    String? orderId,
    String? customerName,
    String? address,
    double? distance,
    int? priority,
    String? estimatedTime,
    String? paymentMode,
    int? bagCount,
    bool? isSelected,
  }) {
    return DeliveryOrderItem(
      orderId: orderId ?? this.orderId,
      customerName: customerName ?? this.customerName,
      address: address ?? this.address,
      distance: distance ?? this.distance,
      priority: priority ?? this.priority,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      paymentMode: paymentMode ?? this.paymentMode,
      bagCount: bagCount ?? this.bagCount,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'customerName': customerName,
      'address': address,
      'distance': distance,
      'priority': priority,
      'estimatedTime': estimatedTime,
      'paymentMode': paymentMode,
      'bagCount': bagCount,
      'isSelected': isSelected,
    };
  }

  factory DeliveryOrderItem.fromJson(Map<String, dynamic> json) {
    return DeliveryOrderItem(
      orderId: json['orderId'] ?? '',
      customerName: json['customerName'] ?? '',
      address: json['address'] ?? '',
      distance: (json['distance'] ?? 0.0).toDouble(),
      priority: json['priority'] ?? 0,
      estimatedTime: json['estimatedTime'] ?? '',
      paymentMode: json['paymentMode'] ?? '',
      bagCount: json['bagCount'] ?? 0,
      isSelected: json['isSelected'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeliveryOrderItem &&
        other.orderId == orderId &&
        other.customerName == customerName &&
        other.address == address &&
        other.distance == distance &&
        other.priority == priority &&
        other.estimatedTime == estimatedTime &&
        other.paymentMode == paymentMode &&
        other.bagCount == bagCount &&
        other.isSelected == isSelected;
  }

  @override
  int get hashCode {
    return Object.hash(
      orderId,
      customerName,
      address,
      distance,
      priority,
      estimatedTime,
      paymentMode,
      bagCount,
      isSelected,
    );
  }

  @override
  String toString() {
    return 'DeliveryOrderItem(orderId: $orderId, customerName: $customerName, '
        'address: $address, distance: $distance, priority: $priority, '
        'estimatedTime: $estimatedTime, paymentMode: $paymentMode, '
        'bagCount: $bagCount, isSelected: $isSelected)';
  }
}

/// Order product model for delivery completion screen
class OrderProduct {
  final String id;
  final String name;
  final int quantity;
  final String category;

  const OrderProduct({
    required this.id,
    required this.name,
    required this.quantity,
    required this.category,
  });

  OrderProduct copyWith({
    String? id,
    String? name,
    int? quantity,
    String? category,
  }) {
    return OrderProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      category: category ?? this.category,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'quantity': quantity,
      'category': category,
    };
  }

  factory OrderProduct.fromJson(Map<String, dynamic> json) {
    return OrderProduct(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      quantity: json['quantity'] ?? 0,
      category: json['category'] ?? '',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderProduct &&
        other.id == id &&
        other.name == name &&
        other.quantity == quantity &&
        other.category == category;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, quantity, category);
  }

  @override
  String toString() {
    return 'OrderProduct(id: $id, name: $name, quantity: $quantity, category: $category)';
  }
}
