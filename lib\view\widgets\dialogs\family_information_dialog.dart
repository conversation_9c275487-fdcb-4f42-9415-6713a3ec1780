import 'package:flutter/material.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:intl/intl.dart';
import 'package:kisankonnect_rider/view/widgets/common/common_widgets.dart';

/// Family Information Dialog for Health Insurance
class FamilyInformationDialog extends StatefulWidget {
  final Function(Map<String, String>)? onContinue;

  const FamilyInformationDialog({
    super.key,
    this.onContinue,
  });

  @override
  State<FamilyInformationDialog> createState() => _FamilyInformationDialogState();

  /// Show the family information dialog
  static Future<Map<String, String>?> show({
    required BuildContext context,
    Function(Map<String, String>)? onContinue,
  }) async {
    return await showDialog<Map<String, String>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return FamilyInformationDialog(onContinue: onContinue);
      },
    );
  }
}

class _FamilyInformationDialogState extends State<FamilyInformationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _spouseNameController = TextEditingController();
  final _spouseDobController = TextEditingController();
  final _childNameController = TextEditingController();
  final _childDobController = TextEditingController();

  final List<Map<String, TextEditingController>> _children = [];

  @override
  void dispose() {
    _spouseNameController.dispose();
    _spouseDobController.dispose();
    _childNameController.dispose();
    _childDobController.dispose();

    // Dispose child controllers
    for (var child in _children) {
      child['name']?.dispose();
      child['dob']?.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFFF0F9FF),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  // Family protection icon from assets
                  CachedAssetImage.icon(
                    assetPath: 'assets/icons/family-protection.png',
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Family information for Health Insurance',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        color: Colors.grey,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Container(
                color: const Color(0xFFF7F8FA), // Light background color
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Spouse Information
                        _buildTextField(
                          controller: _spouseNameController,
                          label: 'Spouse name',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter spouse name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        _buildDateField(
                          controller: _spouseDobController,
                          label: 'DD-MM-YYYY',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please select spouse date of birth';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 24),

                        // Children Information
                        _buildTextField(
                          controller: _childNameController,
                          label: 'Child name',
                          validator: (value) {
                            // Child name is optional
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        _buildDateField(
                          controller: _childDobController,
                          label: 'DD-MM-YYYY',
                          validator: (value) {
                            // Child DOB is optional, but if child name is provided, DOB should be provided
                            if (_childNameController.text.trim().isNotEmpty &&
                                (value == null || value.trim().isEmpty)) {
                              return 'Please select child date of birth';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 8),

                        // Add Child Button
                        Align(
                          alignment: Alignment.centerRight,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: AppColors.green, width: 1),
                              color: Colors.white,
                            ),
                            child: TextButton.icon(
                              onPressed: _addChild,
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              icon: const Icon(
                                Icons.add,
                                color: AppColors.green,
                                size: 18,
                              ),
                              label: const Text(
                                'Add',
                                style: TextStyle(
                                  color: AppColors.green,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // Additional Children List
                        ..._buildChildrenList(),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Continue Button
            Padding(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                width: double.infinity,
                height: 52,
                child: ElevatedButton(
                  onPressed: _onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.green,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        color: Colors.white,
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: label,
          hintStyle: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade500,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildDateField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        color: Colors.white,
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        readOnly: true,
        onTap: () => _selectDate(controller),
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: label,
          hintStyle: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade500,
          ),
          suffixIcon: Container(
            padding: const EdgeInsets.all(12),
            child: const Icon(
              Icons.calendar_today,
              size: 20,
              color: Colors.grey,
            ),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  List<Widget> _buildChildrenList() {
    return _children.asMap().entries.map((entry) {
      final index = entry.key;
      final child = entry.value;

      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Text('Child ${index + 2}', style: AppTextTheme.cardTitle),
                const Spacer(),
                IconButton(
                  onPressed: () => _removeChild(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildTextField(
              controller: child['name']!,
              label: 'Child name',
            ),
            const SizedBox(height: 12),
            _buildDateField(
              controller: child['dob']!,
              label: 'DD-MM-YYYY',
            ),
          ],
        ),
      );
    }).toList();
  }

  void _addChild() {
    setState(() {
      _children.add({
        'name': TextEditingController(),
        'dob': TextEditingController(),
      });
    });
  }

  void _removeChild(int index) {
    setState(() {
      _children[index]['name']?.dispose();
      _children[index]['dob']?.dispose();
      _children.removeAt(index);
    });
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 20)), // 20 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.green,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.text = DateFormat('dd-MM-yyyy').format(picked);
    }
  }

  void _onContinue() {
    if (_formKey.currentState!.validate()) {
      final familyData = <String, String>{
        'spouseName': _spouseNameController.text.trim(),
        'spouseDob': _spouseDobController.text.trim(),
        'childName': _childNameController.text.trim(),
        'childDob': _childDobController.text.trim(),
      };

      // Add additional children data
      for (int i = 0; i < _children.length; i++) {
        familyData['child${i + 2}Name'] = _children[i]['name']!.text.trim();
        familyData['child${i + 2}Dob'] = _children[i]['dob']!.text.trim();
      }

      Navigator.of(context).pop(familyData);
      widget.onContinue?.call(familyData);
    }
  }
}
