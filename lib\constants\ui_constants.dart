/// UI Constants
/// Centralized constants for consistent UI design across the app
library;

import 'package:flutter/material.dart';

/// Common border radius values
class AppBorderRadius {
  static const double small = 4.0;
  static const double medium = 8.0;
  static const double large = 12.0;
  static const double extraLarge = 16.0;
  static const double button = 32.0;
  static const double card = 12.0;
  static const double dialog = 16.0;
  
  // BorderRadius objects for convenience
  static const BorderRadius smallRadius = BorderRadius.all(Radius.circular(small));
  static const BorderRadius mediumRadius = BorderRadius.all(Radius.circular(medium));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(large));
  static const BorderRadius extraLargeRadius = BorderRadius.all(Radius.circular(extraLarge));
  static const BorderRadius buttonRadius = BorderRadius.all(Radius.circular(button));
  static const BorderRadius cardRadius = BorderRadius.all(Radius.circular(card));
  static const BorderRadius dialogRadius = BorderRadius.all(Radius.circular(dialog));
}

/// Common spacing values
class AppSpacing {
  static const double xs = 4.0;
  static const double small = 8.0;
  static const double medium = 16.0;
  static const double large = 24.0;
  static const double extraLarge = 32.0;
  static const double xxl = 48.0;
  
  // EdgeInsets objects for convenience
  static const EdgeInsets xsPadding = EdgeInsets.all(xs);
  static const EdgeInsets smallPadding = EdgeInsets.all(small);
  static const EdgeInsets mediumPadding = EdgeInsets.all(medium);
  static const EdgeInsets largePadding = EdgeInsets.all(large);
  static const EdgeInsets extraLargePadding = EdgeInsets.all(extraLarge);
  
  // Horizontal and vertical spacing
  static const EdgeInsets horizontalMedium = EdgeInsets.symmetric(horizontal: medium);
  static const EdgeInsets verticalMedium = EdgeInsets.symmetric(vertical: medium);
  static const EdgeInsets horizontalLarge = EdgeInsets.symmetric(horizontal: large);
  static const EdgeInsets verticalLarge = EdgeInsets.symmetric(vertical: large);
}

/// Common elevation values
class AppElevation {
  static const double none = 0.0;
  static const double low = 2.0;
  static const double medium = 4.0;
  static const double high = 8.0;
  static const double extraHigh = 16.0;
}

/// Common opacity values
class AppOpacity {
  static const double transparent = 0.0;
  static const double veryLight = 0.1;
  static const double light = 0.2;
  static const double medium = 0.5;
  static const double dark = 0.7;
  static const double veryDark = 0.9;
  static const double opaque = 1.0;
}

/// Common icon sizes
class AppIconSize {
  static const double small = 16.0;
  static const double medium = 24.0;
  static const double large = 32.0;
  static const double extraLarge = 48.0;
  static const double huge = 64.0;
}

/// Common animation durations
class AppDuration {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 1000);
}

/// Common box shadows
class AppShadows {
  static const BoxShadow light = BoxShadow(
    color: Color(0x0A000000), // Colors.black.withValues(alpha: 0.04)
    blurRadius: 8,
    offset: Offset(0, 2),
  );
  
  static const BoxShadow medium = BoxShadow(
    color: Color(0x1A000000), // Colors.black.withValues(alpha: 0.1)
    blurRadius: 12,
    offset: Offset(0, 4),
  );
  
  static const BoxShadow heavy = BoxShadow(
    color: Color(0x33000000), // Colors.black.withValues(alpha: 0.2)
    blurRadius: 16,
    offset: Offset(0, 8),
  );
  
  // Shadow lists for convenience
  static const List<BoxShadow> lightShadow = [light];
  static const List<BoxShadow> mediumShadow = [medium];
  static const List<BoxShadow> heavyShadow = [heavy];
}

/// Common gradient definitions
class AppGradients {
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF4CAF50), // AppColors.green
      Color(0xFF45A049), // Slightly darker green
    ],
  );
  
  static const LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x4D000000), // Colors.black.withValues(alpha: 0.3)
      Color(0x00000000), // Colors.transparent
      Color(0x80000000), // Colors.black.withValues(alpha: 0.5)
    ],
    stops: [0.0, 0.3, 1.0],
  );
}

/// Common breakpoints for responsive design
class AppBreakpoints {
  static const double mobile = 480.0;
  static const double tablet = 768.0;
  static const double desktop = 1024.0;
  static const double largeDesktop = 1440.0;
}

/// Common aspect ratios
class AppAspectRatio {
  static const double square = 1.0;
  static const double landscape = 16 / 9;
  static const double portrait = 9 / 16;
  static const double golden = 1.618;
}

/// Common z-index values for layering
class AppZIndex {
  static const int background = 0;
  static const int content = 1;
  static const int overlay = 10;
  static const int modal = 100;
  static const int tooltip = 1000;
  static const int notification = 10000;
}
