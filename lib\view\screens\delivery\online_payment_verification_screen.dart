import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/controllers/order_management_controller.dart';
import 'package:kisankonnect_rider/models/order_models.dart';
import 'package:kisankonnect_rider/mixins/analytics_mixin.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/utils/controller_utils.dart';
import '../../widgets/common/common_app_bar.dart';

class OnlinePaymentVerificationScreen extends StatefulWidget {
  final String orderId;
  final String customerName;
  final double totalAmount;
  final String paymentMethod; // 'upi', 'card', 'wallet'
  final Order? order;

  const OnlinePaymentVerificationScreen({
    super.key,
    required this.orderId,
    required this.customerName,
    required this.totalAmount,
    required this.paymentMethod,
    this.order,
  });

  @override
  State<OnlinePaymentVerificationScreen> createState() => _OnlinePaymentVerificationScreenState();
}

class _OnlinePaymentVerificationScreenState extends State<OnlinePaymentVerificationScreen> with AnalyticsMixin {
  OrderManagementController? _orderController;

  bool _isVerifying = false;
  bool _paymentVerified = false;
  String _transactionId = '';
  String _paymentStatus = 'pending'; // pending, success, failed

  @override
  void initState() {
    super.initState();
    _initializeController();

    // Track screen view
    trackScreenView('online_payment_verification_screen', properties: {
      'order_id': widget.orderId,
      'customer_name': widget.customerName,
      'total_amount': widget.totalAmount,
      'payment_method': widget.paymentMethod,
    });

    // Auto-verify payment after a delay (simulate real-time verification)
    _startPaymentVerification();
  }

  void _initializeController() {
    _orderController = ControllerUtils.getOrderManagementController();
  }

  void _startPaymentVerification() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _verifyPaymentStatus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: CommonAppBar(
        titleKey: 'paymentVerification',
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeaderSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Payment status section
            _buildPaymentStatusSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Payment details section
            _buildPaymentDetailsSection(),

            const Spacer(),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title
          Row(
            children: [
              Container(
                width: ResponsiveUtils.scale(context, 50),
                height: ResponsiveUtils.scale(context, 50),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getPaymentIcon(),
                  color: Colors.blue,
                  size: ResponsiveUtils.scale(context, 24),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingM(context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Online Payment',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 18),
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'Order ID: ${widget.orderId}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // Customer and amount info
          Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Customer:',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      widget.customerName,
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Amount:',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '₹${widget.totalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 16),
                        fontWeight: FontWeight.bold,
                        color: AppColors.green,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Payment Method:',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      _getPaymentMethodName(),
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStatusSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Status icon and text
          if (_paymentStatus == 'pending') ...[
            SizedBox(
              width: ResponsiveUtils.scale(context, 60),
              height: ResponsiveUtils.scale(context, 60),
              child: CircularProgressIndicator(
                strokeWidth: 4,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingM(context)),
            Text(
              'Verifying Payment...',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 18),
                fontWeight: FontWeight.w600,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingS(context)),
            Text(
              'Please wait while we verify the payment status',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 14),
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ] else if (_paymentStatus == 'success') ...[
            Container(
              width: ResponsiveUtils.scale(context, 60),
              height: ResponsiveUtils.scale(context, 60),
              decoration: BoxDecoration(
                color: AppColors.green,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                color: Colors.white,
                size: ResponsiveUtils.scale(context, 30),
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingM(context)),
            Text(
              'Payment Successful!',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 18),
                fontWeight: FontWeight.w600,
                color: AppColors.green,
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingS(context)),
            Text(
              'Payment has been verified successfully',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 14),
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ] else if (_paymentStatus == 'failed') ...[
            Container(
              width: ResponsiveUtils.scale(context, 60),
              height: ResponsiveUtils.scale(context, 60),
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                color: Colors.white,
                size: ResponsiveUtils.scale(context, 30),
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingM(context)),
            Text(
              'Payment Failed',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 18),
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingS(context)),
            Text(
              'Payment verification failed. Please try again.',
              style: TextStyle(
                fontSize: ResponsiveUtils.fontSize(context, 14),
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsSection() {
    if (_paymentStatus != 'success') return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Details',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),
          _buildDetailRow('Transaction ID:', _transactionId),
          _buildDetailRow('Amount:', '₹${widget.totalAmount.toStringAsFixed(2)}'),
          _buildDetailRow('Payment Method:', _getPaymentMethodName()),
          _buildDetailRow('Status:', 'Success'),
          _buildDetailRow('Time:', _getCurrentTime()),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: ResponsiveUtils.spacingS(context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 14),
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 14),
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return SafeArea(
      child: Column(
        children: [
          if (_paymentStatus == 'success') ...[
            SizedBox(
              width: double.infinity,
              height: ResponsiveUtils.scale(context, 48),
              child: ElevatedButton(
                onPressed: _isVerifying ? null : _completeDelivery,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                  ),
                ),
                child: _isVerifying
                    ? SizedBox(
                        width: ResponsiveUtils.scale(context, 20),
                        height: ResponsiveUtils.scale(context, 20),
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Complete Delivery',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(context, 16),
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ] else if (_paymentStatus == 'failed') ...[
            SizedBox(
              width: double.infinity,
              height: ResponsiveUtils.scale(context, 48),
              child: ElevatedButton(
                onPressed: _retryVerification,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                  ),
                ),
                child: Text(
                  'Retry Verification',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            SizedBox(height: ResponsiveUtils.spacingS(context)),
            SizedBox(
              width: double.infinity,
              height: ResponsiveUtils.scale(context, 48),
              child: OutlinedButton(
                onPressed: _switchToCOD,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.orange),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
                  ),
                ),
                child: Text(
                  'Switch to Cash Payment',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getPaymentIcon() {
    switch (widget.paymentMethod) {
      case 'upi':
        return Icons.qr_code;
      case 'card':
        return Icons.credit_card;
      case 'wallet':
        return Icons.account_balance_wallet;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodName() {
    switch (widget.paymentMethod) {
      case 'upi':
        return 'UPI Payment';
      case 'card':
        return 'Card Payment';
      case 'wallet':
        return 'Digital Wallet';
      default:
        return 'Online Payment';
    }
  }

  String _getCurrentTime() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _verifyPaymentStatus() async {
    setState(() => _isVerifying = true);

    try {
      // Simulate payment verification API call
      await Future.delayed(const Duration(seconds: 2));

      // For demo, randomly succeed or fail
      final success = DateTime.now().millisecond % 2 == 0;

      setState(() {
        _paymentStatus = success ? 'success' : 'failed';
        _paymentVerified = success;
        _transactionId = success ? 'TXN${DateTime.now().millisecondsSinceEpoch}' : '';
        _isVerifying = false;
      });

      // Track verification result
      trackUserAction('payment_verification_result', properties: {
        'order_id': widget.orderId,
        'payment_method': widget.paymentMethod,
        'verification_status': _paymentStatus,
        'transaction_id': _transactionId,
      });
    } catch (e) {
      setState(() {
        _paymentStatus = 'failed';
        _isVerifying = false;
      });

      trackError('payment_verification_error', e.toString());
    }
  }

  Future<void> _completeDelivery() async {
    setState(() => _isVerifying = true);

    try {
      // Complete delivery with online payment
      final success = await _orderController?.completeDelivery(
            widget.orderId,
            paymentMode: PaymentMode.online,
            qrTransactionId: _transactionId,
            deliveryNotes: 'Online payment verified - ${_getPaymentMethodName()}',
          ) ??
          false;

      if (success) {
        // Track successful completion
        trackDeliveryEvent('online_payment_completed', deliveryData: {
          'order_id': widget.orderId,
          'payment_method': widget.paymentMethod,
          'transaction_id': _transactionId,
          'total_amount': widget.totalAmount,
        });

        // Show success message
        Get.snackbar(
          'Delivery Complete',
          'Order delivered with online payment!',
          backgroundColor: AppColors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        // Navigate to order summary
        Future.delayed(const Duration(seconds: 2), () {
          final completedOrders = _orderController?.completedOrders ?? [];
          Get.offNamed(AppRoutes.orderSummary, arguments: {
            'completedOrders': completedOrders,
            'totalEarnings': _orderController?.totalEarningsToday ?? 0.0,
            'totalDeliveries': _orderController?.deliveredOrders ?? 0,
          });
        });
      } else {
        throw Exception('Failed to complete delivery');
      }
    } catch (e) {
      setState(() => _isVerifying = false);
      trackError('online_payment_completion_error', e.toString());

      Get.snackbar(
        'Error',
        'Failed to complete delivery. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _retryVerification() {
    setState(() {
      _paymentStatus = 'pending';
      _paymentVerified = false;
      _transactionId = '';
    });

    _startPaymentVerification();
  }

  void _switchToCOD() {
    // Navigate to COD payment screen
    Get.offNamed('/cod-payment', arguments: {
      'orderId': widget.orderId,
      'customerName': widget.customerName,
      'totalAmount': widget.totalAmount,
      'order': widget.order,
    });
  }
}
