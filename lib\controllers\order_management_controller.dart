import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/order_models.dart';
import '../mixins/analytics_mixin.dart';
import '../services/all_services.dart';

class OrderManagementController extends GetxController with GetXAnalyticsMixin {
  final ApiService _apiService = ApiService.instance;
  
  // Observable state
  final RxList<Order> _assignedOrders = <Order>[].obs;
  final RxList<Order> _activeOrders = <Order>[].obs;
  final RxList<Order> _completedOrders = <Order>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _currentOrderId = ''.obs;
  final Rx<Order?> _currentOrder = Rx<Order?>(null);

  // Getters
  List<Order> get assignedOrders => _assignedOrders;
  List<Order> get activeOrders => _activeOrders;
  List<Order> get completedOrders => _completedOrders;
  bool get isLoading => _isLoading.value;
  String get currentOrderId => _currentOrderId.value;
  Order? get currentOrder => _currentOrder.value;

  // Order statistics
  int get totalOrdersToday => assignedOrders.length + activeOrders.length + completedOrders.length;
  int get pendingOrders => assignedOrders.length + activeOrders.length;
  int get deliveredOrders => completedOrders.where((o) => o.status == OrderStatus.delivered).length;
  double get totalEarningsToday => completedOrders.fold(0.0, (sum, order) => sum + order.totalAmount);

  @override
  void onInit() {
    super.onInit();
    loadOrders();
  }

  /// Load all orders for the current rider
  Future<void> loadOrders() async {
    try {
      _isLoading.value = true;
      
      // Track order loading
      trackUserAction('load_orders');
      
      // Simulate API call - replace with actual API
      await Future.delayed(const Duration(seconds: 1));
      
      // Load sample orders for demo
      _loadSampleOrders();
      
      debugPrint('📦 Loaded ${totalOrdersToday} orders');
      
    } catch (e) {
      debugPrint('❌ Error loading orders: $e');
      trackError('order_loading_error', e.toString());
    } finally {
      _isLoading.value = false;
    }
  }

  /// Accept an assigned order
  Future<bool> acceptOrder(String orderId) async {
    try {
      final orderIndex = _assignedOrders.indexWhere((o) => o.orderId == orderId);
      if (orderIndex == -1) return false;

      final order = _assignedOrders[orderIndex];
      
      // Track order acceptance
      trackOrderEvent('accepted', orderId, additionalData: {
        'total_amount': order.totalAmount,
        'distance': order.distance,
        'customer_type': order.customer.customerType,
      });

      // Update order status
      final updatedOrder = order.copyWith(status: OrderStatus.accepted);
      _assignedOrders[orderIndex] = updatedOrder;
      _activeOrders.add(updatedOrder);
      _assignedOrders.removeAt(orderIndex);

      debugPrint('✅ Order $orderId accepted');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error accepting order: $e');
      trackError('order_acceptance_error', e.toString());
      return false;
    }
  }

  /// Mark order as picked up
  Future<bool> pickupOrder(String orderId) async {
    try {
      final orderIndex = _activeOrders.indexWhere((o) => o.orderId == orderId);
      if (orderIndex == -1) return false;

      final order = _activeOrders[orderIndex];
      
      // Track order pickup
      trackOrderEvent('picked_up', orderId, additionalData: {
        'pickup_time': DateTime.now().toIso8601String(),
        'bag_info': order.bagInfo.toJson(),
      });

      // Update order status
      final updatedOrder = order.copyWith(
        status: OrderStatus.pickedUp,
        pickedUpAt: DateTime.now(),
      );
      _activeOrders[orderIndex] = updatedOrder;

      debugPrint('📦 Order $orderId picked up');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error picking up order: $e');
      trackError('order_pickup_error', e.toString());
      return false;
    }
  }

  /// Start delivery for an order
  Future<bool> startDelivery(String orderId) async {
    try {
      final orderIndex = _activeOrders.indexWhere((o) => o.orderId == orderId);
      if (orderIndex == -1) return false;

      final order = _activeOrders[orderIndex];
      
      // Track delivery start
      trackDeliveryEvent('started', deliveryData: {
        'order_id': orderId,
        'customer_address': order.customer.address,
        'distance': order.distance,
        'start_time': DateTime.now().toIso8601String(),
      });

      // Update order status
      final updatedOrder = order.copyWith(status: OrderStatus.onTheWay);
      _activeOrders[orderIndex] = updatedOrder;
      _currentOrderId.value = orderId;
      _currentOrder.value = updatedOrder;

      debugPrint('🚚 Delivery started for order $orderId');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error starting delivery: $e');
      trackError('delivery_start_error', e.toString());
      return false;
    }
  }

  /// Complete order delivery
  Future<bool> completeDelivery(String orderId, {
    required PaymentMode paymentMode,
    double? cashReceived,
    String? qrTransactionId,
    String? deliveryNotes,
  }) async {
    try {
      final orderIndex = _activeOrders.indexWhere((o) => o.orderId == orderId);
      if (orderIndex == -1) return false;

      final order = _activeOrders[orderIndex];
      
      // Track delivery completion
      trackDeliveryEvent('completed', deliveryData: {
        'order_id': orderId,
        'payment_mode': paymentMode.value,
        'cash_received': cashReceived,
        'qr_transaction_id': qrTransactionId,
        'delivery_time': DateTime.now().toIso8601String(),
        'total_amount': order.totalAmount,
      });

      // Update order status
      final updatedOrder = order.copyWith(
        status: OrderStatus.delivered,
        deliveredAt: DateTime.now(),
        paymentMode: paymentMode,
        cashReceived: cashReceived,
        qrTransactionId: qrTransactionId,
        deliveryNotes: deliveryNotes,
      );
      
      _activeOrders.removeAt(orderIndex);
      _completedOrders.add(updatedOrder);
      
      // Clear current order if it's the one being completed
      if (_currentOrderId.value == orderId) {
        _currentOrderId.value = '';
        _currentOrder.value = null;
      }

      debugPrint('✅ Order $orderId delivered successfully');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error completing delivery: $e');
      trackError('delivery_completion_error', e.toString());
      return false;
    }
  }

  /// Cancel an order
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      Order? order;
      int orderIndex = -1;
      List<Order> sourceList = [];

      // Find order in assigned orders
      orderIndex = _assignedOrders.indexWhere((o) => o.orderId == orderId);
      if (orderIndex != -1) {
        order = _assignedOrders[orderIndex];
        sourceList = _assignedOrders;
      } else {
        // Find order in active orders
        orderIndex = _activeOrders.indexWhere((o) => o.orderId == orderId);
        if (orderIndex != -1) {
          order = _activeOrders[orderIndex];
          sourceList = _activeOrders;
        }
      }

      if (order == null) return false;

      // Track order cancellation
      trackOrderEvent('cancelled', orderId, additionalData: {
        'cancellation_reason': reason,
        'order_status': order.status.value,
        'cancellation_time': DateTime.now().toIso8601String(),
      });

      // Update order status
      final updatedOrder = order.copyWith(
        status: OrderStatus.cancelled,
        cancellationReason: reason,
      );
      
      sourceList.removeAt(orderIndex);
      _completedOrders.add(updatedOrder);

      debugPrint('❌ Order $orderId cancelled: $reason');
      return true;
      
    } catch (e) {
      debugPrint('❌ Error cancelling order: $e');
      trackError('order_cancellation_error', e.toString());
      return false;
    }
  }

  /// Get orders by status
  List<Order> getOrdersByStatus(OrderStatus status) {
    final allOrders = [..._assignedOrders, ..._activeOrders, ..._completedOrders];
    return allOrders.where((order) => order.status == status).toList();
  }

  /// Get order by ID
  Order? getOrderById(String orderId) {
    final allOrders = [..._assignedOrders, ..._activeOrders, ..._completedOrders];
    try {
      return allOrders.firstWhere((order) => order.orderId == orderId);
    } catch (e) {
      return null;
    }
  }

  /// Reorder orders by priority
  void reorderOrders(List<Order> newOrderList) {
    _activeOrders.clear();
    _activeOrders.addAll(newOrderList);
    
    trackUserAction('reorder_orders', properties: {
      'order_count': newOrderList.length,
      'reorder_time': DateTime.now().toIso8601String(),
    });
  }

  /// Load sample orders for demo
  void _loadSampleOrders() {
    final sampleOrders = [
      Order(
        orderId: 'E76592',
        batchId: 'BATCH001',
        customer: CustomerInfo(
          customerId: 'CUST001',
          name: 'Prathamesh Chavan',
          phoneNumber: '+91 9876543210',
          address: 'NLT-B, room no 36, Sector 20, Opposite to Dreamland society, Nerul (w), Navi Mumbai',
          landmark: 'Opposite to Dreamland near Metro Station',
          latitude: 19.0330,
          longitude: 73.0297,
          customerType: 'Gold',
        ),
        items: [
          OrderItem(itemId: 'MILK001', itemName: 'Fresh Milk', quantity: 2, price: 25.0, unit: 'L'),
          OrderItem(itemId: 'BREAD001', itemName: 'Brown Bread', quantity: 1, price: 35.0, unit: 'pack'),
        ],
        bagInfo: DeliveryBagInfo(saddleBags: 1, silverBags: 1, milkPouches: 2, totalRacks: 12),
        status: OrderStatus.assigned,
        paymentMode: PaymentMode.cash,
        priority: OrderPriority.high,
        totalAmount: 85.0,
        distance: 2.5,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        scheduledDeliveryTime: DateTime.now().add(const Duration(hours: 2)),
      ),
      // Add more sample orders...
    ];

    _assignedOrders.addAll(sampleOrders);
  }

  /// Refresh orders
  Future<void> refreshOrders() async {
    await loadOrders();
  }

  /// Clear all orders (for testing)
  void clearOrders() {
    _assignedOrders.clear();
    _activeOrders.clear();
    _completedOrders.clear();
    _currentOrderId.value = '';
    _currentOrder.value = null;
  }
}
