import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/models/order_models.dart';
import 'package:kisankonnect_rider/mixins/analytics_mixin.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import '../../widgets/common/common_app_bar.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String orderId;
  final String customerName;
  final String customerPhone;
  final Order? order;

  const OTPVerificationScreen({
    super.key,
    required this.orderId,
    required this.customerName,
    required this.customerPhone,
    this.order,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> with AnalyticsMixin {
  final List<TextEditingController> _otpControllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  bool _isVerifying = false;
  bool _isResending = false;
  int _resendTimer = 30;
  String _enteredOTP = '';

  @override
  void initState() {
    super.initState();

    // Track screen view
    trackScreenView('otp_verification_screen', properties: {
      'order_id': widget.orderId,
      'customer_name': widget.customerName,
      'customer_phone': widget.customerPhone,
    });

    _startResendTimer();
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _startResendTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendTimer > 0) {
        setState(() => _resendTimer--);
        _startResendTimer();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: CommonAppBar(
        titleKey: 'otpVerification',
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeaderSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // OTP input section
            _buildOTPInputSection(),

            SizedBox(height: ResponsiveUtils.spacingL(context)),

            // Resend section
            _buildResendSection(),

            const Spacer(),

            // Verify button
            _buildVerifyButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title
          Row(
            children: [
              Container(
                width: ResponsiveUtils.scale(context, 50),
                height: ResponsiveUtils.scale(context, 50),
                decoration: BoxDecoration(
                  color: AppColors.green.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.security,
                  color: AppColors.green,
                  size: ResponsiveUtils.scale(context, 24),
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacingM(context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Verify Delivery',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 18),
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'Order ID: ${widget.orderId}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // Customer info
          Container(
            padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'Customer: ${widget.customerName}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ResponsiveUtils.spacingS(context)),
                Row(
                  children: [
                    Icon(
                      Icons.phone_outlined,
                      size: ResponsiveUtils.scale(context, 16),
                      color: Colors.grey.shade600,
                    ),
                    SizedBox(width: ResponsiveUtils.spacingXS(context)),
                    Text(
                      'Phone: ${widget.customerPhone}',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(context, 14),
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // Instructions
          Text(
            'Please ask the customer for the 4-digit OTP to confirm delivery.',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 14),
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOTPInputSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingL(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Enter OTP',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 16),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: ResponsiveUtils.spacingM(context)),

          // OTP input fields
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(4, (index) => _buildOTPField(index)),
          ),
        ],
      ),
    );
  }

  Widget _buildOTPField(int index) {
    return Container(
      width: ResponsiveUtils.scale(context, 50),
      height: ResponsiveUtils.scale(context, 50),
      decoration: BoxDecoration(
        border: Border.all(
          color: _otpControllers[index].text.isNotEmpty ? AppColors.green : Colors.grey.shade300,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.small)),
      ),
      child: TextField(
        controller: _otpControllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: TextStyle(
          fontSize: ResponsiveUtils.fontSize(context, 18),
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
        ),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        onChanged: (value) {
          if (value.isNotEmpty) {
            if (index < 3) {
              _focusNodes[index + 1].requestFocus();
            } else {
              _focusNodes[index].unfocus();
            }
          } else if (value.isEmpty && index > 0) {
            _focusNodes[index - 1].requestFocus();
          }

          _updateEnteredOTP();
        },
      ),
    );
  }

  Widget _buildResendSection() {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.spacingM(context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Didn\'t receive OTP?',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(context, 14),
              color: Colors.grey.shade600,
            ),
          ),
          _resendTimer > 0
              ? Text(
                  'Resend in ${_resendTimer}s',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 14),
                    color: Colors.grey.shade500,
                  ),
                )
              : TextButton(
                  onPressed: _isResending ? null : _resendOTP,
                  child: _isResending
                      ? SizedBox(
                          width: ResponsiveUtils.scale(context, 16),
                          height: ResponsiveUtils.scale(context, 16),
                          child: const CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          'Resend OTP',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(context, 14),
                            fontWeight: FontWeight.w600,
                            color: AppColors.green,
                          ),
                        ),
                ),
        ],
      ),
    );
  }

  Widget _buildVerifyButton() {
    return SafeArea(
      child: SizedBox(
        width: double.infinity,
        height: ResponsiveUtils.scale(context, 48),
        child: ElevatedButton(
          onPressed: (_enteredOTP.length == 4 && !_isVerifying) ? _verifyOTP : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.green,
            disabledBackgroundColor: Colors.grey.shade300,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ResponsiveUtils.borderRadius(context, BorderRadiusType.medium)),
            ),
          ),
          child: _isVerifying
              ? SizedBox(
                  width: ResponsiveUtils.scale(context, 20),
                  height: ResponsiveUtils.scale(context, 20),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  'Verify & Complete Delivery',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }

  void _updateEnteredOTP() {
    setState(() {
      _enteredOTP = _otpControllers.map((controller) => controller.text).join();
    });
  }

  Future<void> _resendOTP() async {
    setState(() => _isResending = true);

    try {
      // Track resend OTP
      trackUserAction('resend_otp', properties: {
        'order_id': widget.orderId,
        'customer_phone': widget.customerPhone,
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _resendTimer = 30;
        _isResending = false;
      });

      _startResendTimer();

      Get.snackbar(
        'OTP Sent',
        'A new OTP has been sent to the customer.',
        backgroundColor: AppColors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() => _isResending = false);
      trackError('resend_otp_error', e.toString());

      Get.snackbar(
        'Error',
        'Failed to resend OTP. Please try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _verifyOTP() async {
    setState(() => _isVerifying = true);

    try {
      // Track OTP verification attempt
      trackUserAction('verify_otp', properties: {
        'order_id': widget.orderId,
        'otp_entered': _enteredOTP,
      });

      // Simulate OTP verification
      await Future.delayed(const Duration(seconds: 2));

      // For demo, accept any 4-digit OTP
      if (_enteredOTP.length == 4) {
        // Track successful verification
        trackDeliveryEvent('otp_verified', deliveryData: {
          'order_id': widget.orderId,
          'verification_method': 'otp',
          'customer_phone': widget.customerPhone,
        });

        // Navigate to delivery completion screen
        Get.offNamed(AppRoutes.deliveryCompletion, arguments: {
          'orderId': widget.orderId,
          'customerName': widget.customerName,
          'order': widget.order,
          'otpVerified': true,
        });
      } else {
        throw Exception('Invalid OTP');
      }
    } catch (e) {
      setState(() => _isVerifying = false);
      trackError('otp_verification_error', e.toString());

      // Clear OTP fields
      for (var controller in _otpControllers) {
        controller.clear();
      }
      _focusNodes[0].requestFocus();

      Get.snackbar(
        'Invalid OTP',
        'Please check the OTP and try again.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
