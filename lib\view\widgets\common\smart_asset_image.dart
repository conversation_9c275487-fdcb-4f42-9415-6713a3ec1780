import 'package:flutter/material.dart';

/// Smart asset image widget that handles both SVG and raster images
/// This widget automatically detects the file type and renders appropriately
class SmartAssetImage extends StatelessWidget {
  /// The asset path (e.g., 'assets/icons/icon.png' or 'assets/icons/icon.svg')
  final String assetPath;
  
  /// Width of the image
  final double? width;
  
  /// Height of the image
  final double? height;
  
  /// How the image should be inscribed into the box
  final BoxFit? fit;
  
  /// Color to apply to the image
  final Color? color;
  
  /// Widget to show on error
  final Widget? errorWidget;
  
  /// Semantic label for the image
  final String? semanticLabel;

  const SmartAssetImage({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.fit,
    this.color,
    this.errorWidget,
    this.semanticLabel,
  });

  /// Factory constructor for icons with common defaults
  factory SmartAssetImage.icon({
    required String assetPath,
    double size = 24.0,
    Color? color,
    BoxFit fit = BoxFit.contain,
    Widget? errorWidget,
    String? semanticLabel,
  }) {
    return SmartAssetImage(
      assetPath: assetPath,
      width: size,
      height: size,
      fit: fit,
      color: color,
      errorWidget: errorWidget,
      semanticLabel: semanticLabel,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if the asset is an SVG file
    final isSvg = assetPath.toLowerCase().endsWith('.svg');
    
    if (isSvg) {
      // For SVG files, provide a proper fallback with icon representation
      return _buildSvgFallback();
    } else {
      // For raster images (PNG, JPG, etc.)
      return _buildRasterImage();
    }
  }

  /// Build fallback for SVG files
  Widget _buildSvgFallback() {
    // Extract icon name from path for better fallback
    final fileName = assetPath.split('/').last.replaceAll('.svg', '');
    final fallbackIcon = _getIconFromFileName(fileName);
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color?.withValues(alpha: 0.1) ?? Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        fallbackIcon,
        size: _getIconSize(),
        color: color ?? Colors.grey.shade600,
        semanticLabel: semanticLabel,
      ),
    );
  }

  /// Build raster image
  Widget _buildRasterImage() {
    return Image.asset(
      assetPath,
      width: width,
      height: height,
      fit: fit ?? BoxFit.contain,
      color: color,
      semanticLabel: semanticLabel,
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? _buildDefaultErrorWidget();
      },
    );
  }

  /// Get appropriate icon size
  double _getIconSize() {
    if (width != null && height != null) {
      return (width! < height! ? width! : height!) * 0.6;
    }
    return width != null ? width! * 0.6 : height != null ? height! * 0.6 : 16;
  }

  /// Get icon based on file name
  IconData _getIconFromFileName(String fileName) {
    final name = fileName.toLowerCase();
    
    // Map common icon names to Material Icons
    if (name.contains('working') || name.contains('time') || name.contains('clock')) {
      return Icons.access_time;
    } else if (name.contains('timer')) {
      return Icons.timer;
    } else if (name.contains('group') || name.contains('people')) {
      return Icons.group;
    } else if (name.contains('home')) {
      return Icons.home;
    } else if (name.contains('profile') || name.contains('person')) {
      return Icons.person;
    } else if (name.contains('settings')) {
      return Icons.settings;
    } else if (name.contains('notification')) {
      return Icons.notifications;
    } else if (name.contains('wallet') || name.contains('money')) {
      return Icons.account_balance_wallet;
    } else if (name.contains('location') || name.contains('map')) {
      return Icons.location_on;
    } else if (name.contains('phone') || name.contains('call')) {
      return Icons.phone;
    } else if (name.contains('mail') || name.contains('email')) {
      return Icons.email;
    } else if (name.contains('star') || name.contains('rating')) {
      return Icons.star;
    } else if (name.contains('heart') || name.contains('favorite')) {
      return Icons.favorite;
    } else if (name.contains('search')) {
      return Icons.search;
    } else if (name.contains('menu')) {
      return Icons.menu;
    } else if (name.contains('close') || name.contains('cancel')) {
      return Icons.close;
    } else if (name.contains('check') || name.contains('done')) {
      return Icons.check;
    } else if (name.contains('add') || name.contains('plus')) {
      return Icons.add;
    } else if (name.contains('edit')) {
      return Icons.edit;
    } else if (name.contains('delete') || name.contains('remove')) {
      return Icons.delete;
    } else if (name.contains('share')) {
      return Icons.share;
    } else if (name.contains('download')) {
      return Icons.download;
    } else if (name.contains('upload')) {
      return Icons.upload;
    } else if (name.contains('camera')) {
      return Icons.camera_alt;
    } else if (name.contains('gallery') || name.contains('image')) {
      return Icons.image;
    } else if (name.contains('video')) {
      return Icons.videocam;
    } else if (name.contains('play')) {
      return Icons.play_arrow;
    } else if (name.contains('pause')) {
      return Icons.pause;
    } else if (name.contains('stop')) {
      return Icons.stop;
    } else if (name.contains('volume')) {
      return Icons.volume_up;
    } else if (name.contains('mic')) {
      return Icons.mic;
    } else if (name.contains('lock')) {
      return Icons.lock;
    } else if (name.contains('unlock')) {
      return Icons.lock_open;
    } else if (name.contains('visibility') || name.contains('eye')) {
      return Icons.visibility;
    } else if (name.contains('refresh') || name.contains('reload')) {
      return Icons.refresh;
    } else if (name.contains('sync')) {
      return Icons.sync;
    } else if (name.contains('cloud')) {
      return Icons.cloud;
    } else if (name.contains('wifi')) {
      return Icons.wifi;
    } else if (name.contains('bluetooth')) {
      return Icons.bluetooth;
    } else if (name.contains('battery')) {
      return Icons.battery_full;
    } else if (name.contains('signal')) {
      return Icons.signal_cellular_4_bar;
    } else {
      // Default fallback icon
      return Icons.image;
    }
  }

  /// Build default error widget
  Widget _buildDefaultErrorWidget() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey.shade400,
        size: _getIconSize(),
      ),
    );
  }
}
