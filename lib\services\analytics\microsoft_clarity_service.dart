import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:clarity_flutter/clarity_flutter.dart';

class MicrosoftClarityService extends GetxService {
  static MicrosoftClarityService get to => Get.find();

  // Microsoft Clarity Project ID
  static const String _clarityProjectId = 'sblv1r50rn';

  // Initialization status
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeClarity();
  }

  /// Initialize Microsoft Clarity
  /// Note: With clarity_flutter, initialization is handled in main.dart via ClarityWidget
  Future<void> _initializeClarity() async {
    try {
      // With clarity_flutter, the initialization is handled by ClarityWidget in main.dart
      // We just need to mark as initialized and track the app launch
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Microsoft Clarity service ready with ID: $_clarityProjectId');
      }

      // Track app launch
      await trackEvent('app_launched', {
        'platform': GetPlatform.isAndroid ? 'android' : 'ios',
        'app_version': '1.0.0', // You can get this from package_info_plus
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Microsoft Clarity service: $e');
      }
    }
  }

  /// Track custom events
  Future<void> trackEvent(String eventName, Map<String, dynamic> properties) async {
    if (!_isInitialized) return;

    try {
      // With clarity_flutter, we can use setCustomTag for tracking
      // Convert event and properties to a meaningful tag
      final eventTag = '$eventName: ${properties.entries.map((e) => '${e.key}=${e.value}').join(', ')}';
      Clarity.setCustomTag(eventTag, eventName);

      if (kDebugMode) {
        print('📊 Clarity Event: $eventName - $properties');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track event $eventName: $e');
      }
    }
  }

  /// Track screen views
  Future<void> trackScreenView(String screenName, {Map<String, dynamic>? properties}) async {
    await trackEvent('screen_view', {
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  /// Track user actions
  Future<void> trackUserAction(String action, {Map<String, dynamic>? properties}) async {
    await trackEvent('user_action', {
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    });
  }

  /// Track order-related events
  Future<void> trackOrderEvent(String orderAction, String orderId, {Map<String, dynamic>? additionalData}) async {
    await trackEvent('order_event', {
      'action': orderAction,
      'order_id': orderId,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    });
  }

  /// Track delivery events
  Future<void> trackDeliveryEvent(String deliveryAction, {Map<String, dynamic>? deliveryData}) async {
    await trackEvent('delivery_event', {
      'action': deliveryAction,
      'timestamp': DateTime.now().toIso8601String(),
      ...?deliveryData,
    });
  }

  /// Track rider status changes
  Future<void> trackRiderStatusChange(bool isOnline, {String? reason}) async {
    await trackEvent('rider_status_change', {
      'status': isOnline ? 'online' : 'offline',
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track earnings events
  Future<void> trackEarningsEvent(String earningsAction, {Map<String, dynamic>? earningsData}) async {
    await trackEvent('earnings_event', {
      'action': earningsAction,
      'timestamp': DateTime.now().toIso8601String(),
      ...?earningsData,
    });
  }

  /// Track navigation events
  Future<void> trackNavigation(String fromScreen, String toScreen) async {
    await trackEvent('navigation', {
      'from_screen': fromScreen,
      'to_screen': toScreen,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track QR scan events
  Future<void> trackQRScanEvent(String scanType, bool successful, {String? orderId}) async {
    await trackEvent('qr_scan', {
      'scan_type': scanType,
      'successful': successful,
      'order_id': orderId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track app errors
  Future<void> trackError(String errorType, String errorMessage, {String? stackTrace}) async {
    await trackEvent('app_error', {
      'error_type': errorType,
      'error_message': errorMessage,
      'stack_trace': stackTrace,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Set user properties
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    if (!_isInitialized) return;

    try {
      // With clarity_flutter, we can use setCustomUserId and setCustomTag for user properties
      for (final entry in properties.entries) {
        if (entry.key == 'user_id' || entry.key == 'rider_id') {
          Clarity.setCustomUserId(entry.value.toString());
        } else {
          Clarity.setCustomTag(entry.key, entry.value.toString());
        }
      }

      if (kDebugMode) {
        print('👤 Clarity User Properties: $properties');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set user properties: $e');
      }
    }
  }

  /// Set rider information
  Future<void> setRiderInfo({
    required String riderId,
    required String riderName,
    String? storeId,
    String? city,
  }) async {
    await setUserProperties({
      'rider_id': riderId,
      'rider_name': riderName,
      'store_id': storeId,
      'city': city,
      'user_type': 'rider',
    });
  }

  /// Track session start
  Future<void> trackSessionStart() async {
    await trackEvent('session_start', {
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track session end
  Future<void> trackSessionEnd() async {
    await trackEvent('session_end', {
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
